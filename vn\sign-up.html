<!doctype html>
    <!--[if lte IE 9]><html lang="en" class="lt-ie10 lt-ie10-msg no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html lang="en" class="no-focus"> <!--<![endif]-->
<head lang="en">
    <title>L2GVE.COM - Create a New Master Account</title><meta property="og: title" content="L2GVE.COM - Create a new Master Account" ><meta property="og: site_name" content="L2GVE.COM" ><meta property="og: type" content="website" ><meta property="og: url" content="http://l2gve.com/en/sign-up" ><meta name="description" content="L2GVE.COM - Create a new Master Account" ><meta property="og: description" content="L2GVE.COM - Create a new Master Account" ><meta property="twitter: description" content="L2GVE.COM - Create a new Master Account" ><meta name="keywords" content="mmoweb, mmoweb4" ><meta charset="utf-8" ><meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" ><meta property="og:image" content="/template/panel/assets/media/favicon/apple-touch-icon-180x180.png" ><link rel="shortcut icon" href="../template/panel/assets/media/favicon/favicon.ico" ><link rel="icon" type="image/png" sizes="180x180" href="../template/panel/assets/media/favicon/apple-touch-icon-180x180.png" ><link rel="icon" type="image/png" sizes="16x16" href="../template/panel/assets/media/favicon/favicon-16x16.png" ><link rel="apple-touch-icon" sizes="32x32" href="../template/panel/assets/media/favicon/favicon-32x32.png" ><link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Muli:300,400,400i,600,700" ><link id="css-main" rel="stylesheet" href="../template/panel/assets/css/codebase.css@v=**********.css" ><link rel="stylesheet" href="../template/panel/assets/css/custom.css@v=**********.css" ><script src="https://www.googletagmanager.com/gtag/js?id=UA-167385217-1" async="true" ></script><script>window.dataLayer = window.dataLayer || []; 
            function gtag(){dataLayer.push(arguments);} 
            gtag('js', new Date()); gtag('config', 'UA-167385217-1',{ 'anonymize_ip': true });</script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-T99S9NM7');</script><script>   (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
               m[i].l=1*new Date();k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
               (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");
               ym(97762192, "init", {
                    clickmap:true,
                    trackLinks:true,
                    accurateTrackBounce:true,
                    webvisor:true
               });</script><noscript><div><img src="https://mc.yandex.ru/watch/97762192" style="position:absolute; left:-9999px;" alt="" /></div></noscript>    <!-- Stylesheets -->
                    <!-- END Stylesheets -->

        <link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
<link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />
    <link rel="stylesheet" href="../template/panel/assets/css/gwstyle.css@v=1714918634.css">
    </head>
    <body>
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T99S9NM7" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>        <!-- Page Container-->
            <div id="page-container" class="sidebar-inverse side-scroll main-content-boxed enable-page-overlay side-trans-enabled ">
            <!-- Sidebar -->
<nav id="sidebar">
    <!-- Sidebar Scroll Container -->
    <div id="sidebar-scroll">
        <!-- Sidebar Content -->
        <div class="sidebar-content">
            <!-- Side Header -->
            <div class="content-header content-header-fullrow bg-black-op-10">
                <div class="content-header-section text-center align-parent">
                    <button type="button" class="btn btn-circle btn-dual-secondary d-lg-none align-v-r" data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times text-danger"></i>
                    </button>
                    <!-- END Close Sidebar -->

                    <!-- Logo -->
                    <div class="content-header-item">
                        <a class="link-effect font-w700" href="index.html">
                            <span class="font-size-xl text-dual-primary-dark">L2GVE.COM</span>
                        </a>
                    </div>
                    <!-- END Logo -->
                </div>
            </div>
            <!-- END Side Header -->

            <!-- Side Main Navigation -->
            <div class="content-side content-side-full">

                <ul class="nav-main">
                                        <li class=""><a href="rating.html" target="" lv_menu="1" class=" " ><i class="fa fa-line-chart"></i><span class="sidebar-mini-hide">Rankings</span></a></li><li class=""><a href="donations.html" target="" lv_menu="300" class=" " ><i class="si si-diamond"></i><span class="sidebar-mini-hide">Donations</span></a></li><li class=""><a href="../index.html" target="_blank" lv_menu="500" class=" " ><i class="si si-action-undo"></i><span class="sidebar-mini-hide">Back to the main site</span></a></li>                </ul>
            </div>
            <!-- END Side Main Navigation -->
        </div>
        <!-- Sidebar Content -->
    </div>
    <!-- END Sidebar Scroll Container -->
</nav>
<!-- END Sidebar -->
<!-- Header -->
<header id="page-header">
    <!-- Header Content -->
    <div class="content-header">
        <!-- Left Section -->
        <div class="content-header-section">

                        <div class="content-header-item">
                <a href="index.html" class="logo">
                    <img src="../template/panel/assets/media/logo/logo.png@v1" alt="logo" class="logo__img">
                </a>
            </div>
                        <div class="content-header-item">

                <ul class="nav-main-header">
                                    </ul>
            </div>
        </div>
        <!-- END Left Section -->

        <!-- Middle Section -->
        <div class="content-header-section d-none d-lg-block">
            <ul class="nav-main-header">
                <li class=""><a href="rating.html" target="" lv_menu="1" class=" " ><i class="fa fa-line-chart"></i><span class="sidebar-mini-hide">Rankings</span></a></li><li class=""><a href="donations.html" target="" lv_menu="300" class=" " ><i class="si si-diamond"></i><span class="sidebar-mini-hide">Donations</span></a></li><li class=""><a href="../index.html" target="_blank" lv_menu="500" class=" " ><i class="si si-action-undo"></i><span class="sidebar-mini-hide">Back to the main site</span></a></li>            </ul>
            <!-- END Header Navigation -->
        </div>
        <!-- END Middle Section -->

        <!-- Right Section -->
        <div class="content-header-section">


            

                <a href="sign-in.html" class="btn btn-dual-secondary mr-5 ">
                    <i class="si si-login"></i> Sign Ιn                </a>
                <a href="sign-up.html" class="btn btn-dual-secondary active">
                    <i class="fa fa-user-plus"></i> Register                </a>
            
            <!-- Toggle Sidebar -->
            <button type="button" class="btn btn-circle btn-dual-secondary d-lg-none" data-toggle="layout" data-action="sidebar_toggle">
                <i class="fa fa-navicon"></i>
            </button>
            <!-- END Toggle Sidebar -->
        </div>
        <!-- END Right Section -->
    </div>
    <!-- END Header Content -->

        <!-- Header Loader -->
        <div id="page-header-loader" class="overlay-header bg-primary">
            <div class="content-header content-header-fullrow text-center">
                <div class="content-header-item">
                    <i class="fa fa-sun-o fa-spin text-white"></i>
                </div>
            </div>
        </div>
        <!-- END Header Loader -->

</header>
<!-- END Header -->            <!-- Main Container -->
            <main id="main-container">
                                                    <div class="row mx-0 justify-content-center">
    <div class="col-lg-6 col-xl-4">
        <div class="content content-full overflow-hidden">
            <!-- Header -->
            <div class="pb-20 text-center">
                <h1 class="h4 font-w700 mt-30 mb-10">Welcome to the Account Panel</h1>
            </div>
            <!-- END Header -->

            <form class="js-validation-signin" action="https://l2gve.com/input" method="post" id="form_signup" onsubmit="return false;">
                <input type="hidden" name="module_form" value="Modules\Globals\SignUp\SignUp"><input type="hidden" name="module" value="signup">                <input id="type_reg" name="type_reg" type="hidden" value="#r-email">

                <div class="block block-themed block-rounded block-shadow ">

                                        <div class="block-header bg-gd-emerald">
                        <h3 class="block-title">Create a new Master Account</h3>
                        <div class="block-options">
                            <select class="form-control font-size-xs" id="change_lang" style="height: 30px;">
                                                                    <option hreflang="ru" value="/ru/sign-up" >RUS</option>
                                                                    <option hreflang="en" value="/en/sign-up" selected>ENG</option>
                                                            </select>
                        </div>
                    </div>
                    

                    <ul class="nav nav-tabs nav-tabs-alt row no-gutters" data-toggle="tabs" role="tablist">


                                                                                                                        <li class="nav-item col-12">
                            <a class="nav-link active " href="sign-up.html#r-email"><i class="si si-envelope"></i> E-mail Address</a>
                        </li>
                        
                        
                    </ul>

                    <div class="block-content">
                        
                            <div class="form-group add-login" >
                                <label for="t-signup-server">Select Server</label>
                                                                                                <select class="form-control" id="t-signup-server" name="sid">
                                                                                                                                                                        <option value="824" >Interlude  [x30]</option>
                                                                                                                                                        </select>
                            </div>

                            <div class="form-group row add-login" >
                                <div class="col-12">
                                    <label for="t-signup-login">ID</label>
                                    <div class="input-group">
                                         
                                            <div class="input-group-prepend">
                                                <select class="form-control prefix_select" data-toggle="tooltip" data-placement="top" name="prefix" title="Prefix (it will be added before your ID)" style="border-radius: .25rem 0 0 .25rem;">
                                                                                                            <option value="CZ_" selected>CZ_</option>
                                                                                                            <option value="OR_" >OR_</option>
                                                                                                            <option value="ZH_" >ZH_</option>
                                                                                                            <option value="IF_" >IF_</option>
                                                                                                            <option value="HD_" >HD_</option>
                                                                                                            <option value="GC_" >GC_</option>
                                                                                                    </select>
                                            </div>
                                            <div class="input-group-prepend">
                                                <button type="button" class="btn btn-secondary submit-btn" title="Refresh prefix" data-post="refresh=prefix" data-action="/prefix/refresh" >
                                                    <i class="fa fa-refresh"></i>
                                                </button>
                                            </div>
                                        
                                        <input type="text" class="form-control" id="t-signup-login" name="login" placeholder="Login">
                                    </div>
                                </div>
                            </div>
                                                    
                                                <div class="tab-content">
                                                        <div class="tab-pane active " id="r-email" role="tabpanel">

                                <div class="form-group row">
                                    <div class="col-12">
                                        <label for="signup-email">E-mail</label>
                                        <input type="email" class="form-control" id="signup-email" name="email" placeholder="<EMAIL>">
                                                                                    <div class="form-text text-muted">A confirmation email will be sent to the specified email address.</div>
                                        
                                    </div>
                                </div>

                            </div>
                                                                                </div>




                                                    <div class="form-group row">
                                <div class="col-12">
                                    <label for="t-signup-password">Password</label>
                                    <div class="input-group">

                                        <input type="password" class="form-control" id="t-signup-password" name="password" placeholder="********">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-secondary" id="eye">
                                                <i class="fa fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            

                            <div class="form-group row mb-0">
                                <div class="col-sm-12 push">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="signup-terms" name="terms">
                                        <label class="custom-control-label" for="signup-terms">I have read and accept the User Agreement</label>
                                    </div>
                                                                    </div>
                                <div class="col-sm-12 text-sm-right push">

                                    <button type="submit" class="btn btn-alt-success submit-form">
                                       Create Master Account                                    </button>
                                </div>
                            </div>
                                            </div>
                    <div class="block-content bg-body-light">
                        <div class="form-group text-center">
                            <a class="link-effect text-muted mr-10 mb-5 d-inline-block" href="https://l2gve.com/terms" target="_blank">
                                <i class="fa fa-book text-muted mr-5"></i> Read the User Agreement                            </a>
                            <a class="link-effect text-muted mr-10 mb-5 d-inline-block" href="sign-in.html">
                                <i class="fa fa-user text-muted mr-5"></i> Sign in                            </a>
                        </div>
                    </div>
                </div>
            </form>
            <!-- END Sign Up Form -->

        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function (event) {


       /* function onSubmitReInv(token) {
            $('#captcha').val(token);
            document.getElementById("form_signup").submit();

        }*/

        $('.nav-tabs').on('click', '.nav-link',function(){
            $('#type_reg').val($(this).attr('href'));
        });
        $('.nav-link.active').click();

                $("#eye").click(function () {
            const this__ = $(this).find('.fa');
            const password = $("#t-signup-password");

            if (password.attr("type") === "password") {
                password.attr("type", "text");
                this__.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                password.attr("type", "password");
                this__.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });

    });
</script>                            </main>
            <!-- END Main Container -->
                                <!-- Pop Out Modal -->
                <div class="modal fade" id="modal-ajax" tabindex="-1" role="dialog" aria-labelledby="modal-popout" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-popout" role="document">
                        <div class="modal-content">
                            <form class="modal-ajax-form" action="https://l2gve.com/input" method="post" onsubmit="return false;">
                                <div class="block block-themed block-transparent mb-0">
                                    <div class="block-header bg-primary-dark">
                                        <h3 class="block-title modal-ajax-title"></h3>
                                        <div class="block-options">
                                            <button type="button" class="btn-block-option" data-dismiss="modal" aria-label="Close">
                                                <i class="si si-close"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="block-content modal-ajax-content"></div>
                                </div>
                                <div class=" modal-ajax-footer block-content block-content-sm block-content-full bg-body-light block-settings-save-fix"></div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- END Pop Out Modal -->
        </div>
        <!-- END Page Container -->
        <!-- Core JS -->
        <script src="../template/panel/assets/js/codebase.core.min.js@v=**********" ></script><script src="../template/panel/assets/js/codebase.app.min.js@v=**********" ></script><script src="../template/panel/assets/js/plugins/bootstrap-notify/bootstrap-notify.min.js" ></script><script src="../template/panel/assets/js/plugins/bootstrap-history-tabs/bootstrap-history-tabs.js@v=2" ></script><script src="../template/panel/assets/js/plugins/masonry/masonry.pkgd.min.js" ></script><script src="../template/panel/assets/js/mmoweb.js@v=**********" ></script><script src="https://mmoweb.biz/watch.js" ></script><script>window.masonry_div = $('.grid').masonry({itemSelector: '.grid-item',columnWidth: '.grid-sizer',percentPosition: true});$('.nav-tabs a').historyTabs();</script>    </body>
</html>