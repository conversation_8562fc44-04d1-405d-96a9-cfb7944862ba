<?php
// <PERSON>ript để thay thế l2eirin thành l2gve trong toàn bộ dự án

function replaceInFile($filePath, $search, $replace) {
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        $newContent = str_replace($search, $replace, $content);
        if ($content !== $newContent) {
            file_put_contents($filePath, $newContent);
            echo "Updated: $filePath\n";
        }
    }
}

function replaceInDirectory($dir, $search, $replace) {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );

    foreach ($files as $file) {
        if ($file->isFile()) {
            $extension = $file->getExtension();
            
            // Chỉ xử lý các file text
            if (in_array($extension, ['html', 'php', 'js', 'css', 'sql', 'txt', 'md'])) {
                replaceInFile($file->getPathname(), $search, $replace);
            }
        }
    }
}

// <PERSON><PERSON><PERSON> thay thế cần thực hiện
$replacements = [
    // Branding text
    'l2eirin' => 'l2gve',
    'L2Eirin' => 'L2GVE', 
    'L2EIRIN' => 'L2GVE',
    'Eirin' => 'GVE',
    'eirin' => 'gve',
    
    // Đường dẫn thư mục
    'template/site/l2eirin/' => 'template/site/l2gve/',
    '/template/site/l2eirin/' => '/template/site/l2gve/',
    '../template/site/l2eirin/' => '../template/site/l2gve/',
    
    // Domain names
    'l2eirin.com' => 'l2gve.com',
    'L2EIRIN.COM' => 'L2GVE.COM'
];

echo "<h2>Starting Branding Replacement...</h2>\n";

// Thực hiện thay thế
foreach ($replacements as $search => $replace) {
    echo "Replacing '$search' with '$replace'...\n";
    replaceInDirectory('.', $search, $replace);
}

echo "\n<h3>Branding replacement completed!</h3>\n";
echo "<p>Don't forget to manually rename the folder:</p>\n";
echo "<code>template/site/l2eirin/</code> → <code>template/site/l2gve/</code>\n";
?>
