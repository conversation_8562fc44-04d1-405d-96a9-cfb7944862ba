<?php
declare(strict_types=1);

require_once __DIR__ . '/../config/asset_manager.php';

function renderOptimizedHead(string $title, string $description, string $language = 'en'): string {
    $assetManager = AssetManager::createDefault();
    
    // Generate cache-busted version
    $version = filemtime(__DIR__ . '/../config/asset_manager.php');
    
    ob_start();
    ?>
<!DOCTYPE html>
<html lang="<?= htmlspecialchars($language) ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://www.googletagmanager.com">
    <link rel="preconnect" href="https://mc.yandex.ru">
    
    <!-- DNS prefetch for performance -->
    <link rel="dns-prefetch" href="//l2gve.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    
    <!-- Critical resource hints -->
    <?= $assetManager->renderPreloadTags() ?>
    
    <!-- Meta tags -->
    <title><?= htmlspecialchars($title) ?></title>
    <meta name="description" content="<?= htmlspecialchars($description) ?>">
    <meta name="theme-color" content="#0f1416">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico?v=1" type="image/x-icon">
    <link rel="icon" sizes="32x32" href="../template/site/l2gve/images/favicon/favicon-32x32.png?v=1" type="image/png">
    <link rel="apple-touch-icon" href="../template/site/l2gve/images/favicon/apple-touch-icon.png?v=1">
    
    <!-- Open Graph -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="<?= htmlspecialchars($title) ?>">
    <meta property="og:description" content="<?= htmlspecialchars($description) ?>">
    <meta property="og:image" content="/template/site/l2gve/images/sclbnr-<?= $language ?>.jpg">
    <meta property="og:locale" content="<?= $language === 'vn' ? 'vi_VN' : 'en_US' ?>">
    
    <!-- Critical CSS inlined -->
    <?= $assetManager->renderCSSLinks(true) ?>
    
    <!-- Non-critical CSS loaded asynchronously -->
    <script>
    // Load non-critical CSS asynchronously
    function loadCSS(href) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        document.head.appendChild(link);
    }
    
    // Load after page load
    window.addEventListener('load', function() {
        // Load remaining CSS files
        setTimeout(() => {
            <?php
            $nonCriticalCSS = [
                '../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css',
                '../template/site/l2gve/libs/swiper/css/swiper.min.css'
            ];
            foreach ($nonCriticalCSS as $css): ?>
            loadCSS('<?= $css ?>?v=<?= $version ?>');
            <?php endforeach; ?>
        }, 100);
    });
    </script>
    
    <!-- Critical JavaScript -->
    <script>
    // Performance monitoring
    window.performance && performance.mark && performance.mark('head-start');
    
    // Critical configuration
    window.__config = {
        version: '<?= $version ?>',
        language: '<?= $language ?>',
        gFonts: {
            fonts: ["Open Sans:400,500,600,700:latin,vietnamese"],
            delay: 500,
        },
        preload: {
            minTime: 1, // Reduced preload time
        },
        sectionSwitcher: {
            init: true,
            speed: 0.4,
            easeType: "power3.out",
        },
        performance: {
            lazyLoad: true,
            imageOptimization: true,
            prefetchOnHover: true
        }
    };
    
    // Local development check
    if (window.location.hostname === 'l2gve.local' || window.location.hostname === 'localhost') {
        console.log('Local development mode - optimizations enabled');
        window.DISABLE_DOMAIN_CHECK = true;
    }
    
    // Service Worker registration for caching
    if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(reg => console.log('SW registered'))
                .catch(err => console.log('SW registration failed'));
        });
    }
    </script>
    
    <!-- Analytics (async) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-167385217-1"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'UA-167385217-1', {
        'anonymize_ip': true,
        'send_page_view': false // Manual page view tracking
    });
    
    // Send page view after load
    window.addEventListener('load', () => {
        gtag('event', 'page_view', {
            'page_title': document.title,
            'page_location': window.location.href
        });
    });
    </script>
    
    <!-- GTM -->
    <script>
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-T99S9NM7');
    </script>
    
    <!-- Yandex Metrica -->
    <script>
    (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
    m[i].l=1*new Date();k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
    (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");
    ym(97762192, "init", {
        clickmap:true,
        trackLinks:true,
        accurateTrackBounce:true,
        webvisor:true,
        defer:true
    });
    </script>
    
    <noscript>
        <div><img src="https://mc.yandex.ru/watch/97762192" style="position:absolute; left:-9999px;" alt="" /></div>
    </noscript>
</head>
    <?php
    return ob_get_clean();
}

function renderOptimizedFooter(): string {
    $assetManager = AssetManager::createDefault();
    $version = filemtime(__DIR__ . '/../config/asset_manager.php');
    
    ob_start();
    ?>
    <!-- Optimized JavaScript loading -->
    <?= $assetManager->renderJSScripts() ?>
    
    <!-- Performance monitoring -->
    <script>
    window.addEventListener('load', function() {
        if (window.performance && performance.mark) {
            performance.mark('page-loaded');
            performance.measure('page-load-time', 'navigationStart', 'page-loaded');
            
            // Log performance metrics
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                console.log('Page Load Time:', perfData.loadEventEnd - perfData.navigationStart, 'ms');
                console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.navigationStart, 'ms');
                console.log('First Paint:', performance.getEntriesByType('paint')[0]?.startTime || 'N/A', 'ms');
            }
        }
    });
    </script>
    
    <!-- GTM noscript -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T99S9NM7" 
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    <?php
    return ob_get_clean();
}
?>
