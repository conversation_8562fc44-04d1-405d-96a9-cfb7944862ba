// Add this script before other scripts in index.html
(function() {
    // Override domain protection
    const originalLocation = window.location;
    Object.defineProperty(window, 'location', {
        get: function() {
            return {
                ...originalLocation,
                hostname: 'l2gve.com' // Fake the hostname
            };
        },
        set: function(url) {
            if (url.includes('get-web.site/protection')) {
                console.log('Domain protection bypassed for local development');
                return;
            }
            originalLocation.href = url;
        }
    });
})();