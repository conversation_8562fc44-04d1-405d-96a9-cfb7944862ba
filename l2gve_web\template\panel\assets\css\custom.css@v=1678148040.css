.options-container .options-overlay-show {
	opacity: 1;
	visibility: visible;
}

/* чекбоксы платежного сервиса */
.cc-selector-2 input {
	position: absolute;
	z-index: 999;
}

.cc-selector-2 input:active + .drinkcard-cc {
	opacity: 0.9;
}

.cc-selector-2 input:checked + .drinkcard-cc {
	-webkit-filter: none;
	-moz-filter: none;
	filter: none;
}

.drinkcard-cc {
	cursor: pointer;
	-webkit-transition: all 100ms ease-in;
	-moz-transition: all 100ms ease-in;
	transition: all 100ms ease-in;
	filter: opacity(0.5);
}

.drinkcard-cc:hover {
	filter: opacity(1);
}

.drinkcard-cc img {
	border-radius: 4px;
}

/* чекбоксы платежного сервиса конец*/

/* список аккаунтов*/
.list_account div.accordion_account::before {
	content: "\f105";
	opacity: 0.4;
}

.list_account.open > div.accordion_account::before {
	opacity: 0;
	-webkit-transform: rotate(90deg);
	transform: rotate(90deg);
}

.list_account div.accordion_account::after {
	content: "\f103";
	opacity: 0;
	-webkit-transform: rotate(-90deg);
	transform: rotate(-90deg);
}

.list_account.open > div.accordion_account::after {
	opacity: 0.4;
	-webkit-transform: rotate(0);
	transform: rotate(0);
}

.list_account div.accordion_account::before,
.list_account div.accordion_account::after {
	position: absolute;
	left: 21px;
	width: 20px;
	height: 20px;
	display: block;
	text-align: center;
	font-family: FontAwesome, "Font Awesome 5 Free", "Font Awesome 5 Pro";
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* список аккаунтов конец*/
.btn-create-account {
	position: absolute;
	padding: 0.3571429rem 0.64285714rem;
	font-size: 0.9286rem;
	line-height: 20px;
	z-index: 2;
	top: 10px;
	right: -8px;
	padding-left: 12px;
	padding-right: 12px;
}

/* Кастомные выбор
https://bootsnipp.com/snippets/gvlK7
*/
.form-group input[type="radio"],
.form-group input[type="checkbox"] {
	display: none;
}

.form-group input[type="checkbox"] + .btn-group > label,
.form-group input[type="radio"] + .btn-group > label {
	white-space: normal;
}

.form-group input[type="checkbox"] + .btn-group > label.btn-default,
.form-group input[type="radio"] + .btn-group > label.btn-default {
	color: #333;
	background-color: #fff;
	border-color: #ccc;
	cursor: pointer;
}

.form-group input[type="radio"] + .btn-group > label span:first-child,
.form-group input[type="checkbox"] + .btn-group > label span:first-child {
	display: none;
}

.form-group input[type="radio"] + .btn-group > label span:first-child + span,
.form-group
	input[type="checkbox"]
	+ .btn-group
	> label
	span:first-child
	+ span {
	display: inline-block;
}

.form-group input[type="radio"]:checked + .btn-group > label span:first-child,
.form-group
	input[type="checkbox"]:checked
	+ .btn-group
	> label
	span:first-child {
	display: inline-block;
}

.form-group
	input[type="radio"]:checked
	+ .btn-group
	> label
	span:first-child
	+ span,
.form-group
	input[type="checkbox"]:checked
	+ .btn-group
	> label
	span:first-child
	+ span {
	display: none;
}

.form-group input[type="checkbox"] + .btn-group > label span[class*="fa-"],
.form-group input[type="radio"] + .btn-group > label span[class*="fa-"] {
	width: 15px;
	float: left;
	margin: 4px 0 2px -2px;
}

.form-group input[type="checkbox"] + .btn-group > label div.content-label,
.form-group input[type="radio"] + .btn-group > label div.content-label {
	margin-left: 22px;
}

/* Кастомные выбор конец*/

/*Статическая Таблица скидок*/
.pricemoney {
	display: flex;
	justify-content: center;
	background: transparent;
	margin: 1px;
	padding: 1px;
}

.pricemoney_block {
	text-align: center;
	display: flex;
	justify-content: center;
	background: rgba(255, 255, 255, 0.11);
	margin: 2px;
	padding: 5px;
	border-radius: 3px;
}

.pricemoney_block-title {
	background: rgba(0, 0, 0, 0.278);
	display: inline-block;
	padding: 5px 10px;
	border-radius: 3px;
	text-transform: uppercase;
	font-size: 12px;
	line-height: 21px;
}

.pricemoney_block-content {
	background: rgba(0, 0, 0, 0.278);
	display: inline-block;
	width: 100px;
	padding: 5px 5px 3px 5px;
	border: 1px solid transparent;
	border-radius: 3px;
	margin-left: 2px;
}

.pricemoney_block:nth-child(3) > .pricemoney_block-content {
	background: rgb(245, 178, 62);
	color: #040404;
	font-weight: bold;
}

.price {
	padding-left: 15px;
	padding-right: 15px;
	box-sizing: border-box;
}

.price h4 {
	text-align: center;
}

.price span {
	display: block;
	text-align: center;
	margin-bottom: 10px;
}

.price b {
	color: rgb(245, 178, 62);
}

@media (max-width: 640px) {
	.pricemoney_block {
		display: block;
		margin: auto;
	}

	.pricemoney_block-content {
		width: 50px;
	}
}
