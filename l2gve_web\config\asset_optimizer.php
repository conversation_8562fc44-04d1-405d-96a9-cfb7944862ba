<?php
declare(strict_types=1);

interface CacheInterface {
    public function get(string $key): ?string;
    public function set(string $key, string $value, int $ttl): bool;
    public function delete(string $key): bool;
}

class AssetOptimizer {
    private const CACHE_VERSION = '2.0';

    public function __construct(
        private readonly CacheInterface $cache = CacheManager::getInstance(),
        private readonly string $baseDir = __DIR__ . '/..'
    ) {}

    public function generateAssetBundle(array $cssFiles, array $jsFiles, string $bundleName): array {
        $cssHash = $this->generateBundle($cssFiles, 'css', $bundleName);
        $jsHash = $this->generateBundle($jsFiles, 'js', $bundleName);

        return [
            'css' => "/cache/bundles/{$bundleName}.{$cssHash}.css",
            'js' => "/cache/bundles/{$bundleName}.{$jsHash}.js",
            'preload' => $this->generatePreloadLinks($cssFiles, $jsFiles)
        ];
    }

    private function generateBundle(array $files, string $type, string $bundleName): string {
        $content = '';
        $fileHashes = [];

        foreach ($files as $file) {
            $fullPath = $this->resolveFilePath($file);
            if (file_exists($fullPath)) {
                $fileContent = file_get_contents($fullPath);
                $fileHashes[] = md5($fileContent);

                if ($type === 'css') {
                    $content .= $this->optimizeCSS($fileContent, dirname($file));
                } else {
                    $content .= $this->optimizeJS($fileContent);
                }
                $content .= "\n";
            }
        }

        $bundleHash = substr(md5(implode('', $fileHashes) . self::CACHE_VERSION), 0, 8);
        $bundlePath = $this->baseDir . "/cache/bundles/{$bundleName}.{$bundleHash}.{$type}";

        // Create bundle directory if not exists
        $bundleDir = dirname($bundlePath);
        if (!is_dir($bundleDir)) {
            mkdir($bundleDir, 0755, true);
        }

        // Write bundle file
        file_put_contents($bundlePath, $content);

        return $bundleHash;
    }

    private function optimizeCSS(string $css, string $basePath = ''): string {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);

        // Fix relative URLs
        if ($basePath) {
            $css = preg_replace_callback('/url\(["\']?([^"\']+)["\']?\)/', function($matches) use ($basePath) {
                $url = $matches[1];
                if (!str_starts_with($url, 'http') && !str_starts_with($url, '/')) {
                    return "url('{$basePath}/{$url}')";
                }
                return $matches[0];
            }, $css);
        }

        // Minify
        $css = preg_replace('/\s+/', ' ', $css);
        $css = str_replace(['; ', ' {', '{ ', ' }', '} ', ': ', ', '], [';', '{', '{', '}', '}', ':', ','], $css);

        return trim($css);
    }

    private function optimizeJS(string $js): string {
        // Basic JS minification (for production, consider using a proper JS minifier)
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js); // Remove multi-line comments
        $js = preg_replace('/\/\/.*$/m', '', $js); // Remove single-line comments
        $js = preg_replace('/\s+/', ' ', $js); // Compress whitespace

        return trim($js);
    }

    private function resolveFilePath(string $file): string {
        if (str_starts_with($file, '/')) {
            return $this->baseDir . $file;
        }
        if (str_starts_with($file, '../')) {
            return $this->baseDir . '/' . $file;
        }
        return $file;
    }

    private function generatePreloadLinks(array $cssFiles, array $jsFiles): array {
        $preloads = [];

        // Preload critical CSS
        foreach (array_slice($cssFiles, 0, 2) as $file) {
            $preloads[] = "<link rel=\"preload\" href=\"{$file}\" as=\"style\">";
        }

        // Preload critical JS
        foreach (array_slice($jsFiles, 0, 3) as $file) {
            $preloads[] = "<link rel=\"preload\" href=\"{$file}\" as=\"script\">";
        }

        return $preloads;
    }

    public function generateCriticalCSS(string $html, array $cssFiles): string {
        // Extract above-the-fold CSS (simplified implementation)
        $criticalSelectors = [
            'body', 'html', '.preload', '.header', '.navigation',
            '.container', '.section', '.bg', '.main'
        ];

        $criticalCSS = '';
        foreach ($cssFiles as $file) {
            $fullPath = $this->resolveFilePath($file);
            if (file_exists($fullPath)) {
                $css = file_get_contents($fullPath);
                foreach ($criticalSelectors as $selector) {
                    if (preg_match("/({$selector}[^{]*\{[^}]*\})/", $css, $matches)) {
                        $criticalCSS .= $matches[1] . "\n";
                    }
                }
            }
        }

        return $this->optimizeCSS($criticalCSS);
    }
}