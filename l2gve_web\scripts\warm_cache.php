<?php
declare(strict_types=1);

require_once '../config/autoload.php';
require_once '../config/page_controller.php';

$controller = new PageController();
$languages = ['en', 'vn'];
$pages = [PageType::WIKI, PageType::INDEX];

echo "Warming cache...\n";

foreach ($languages as $lang) {
    foreach ($pages as $page) {
        try {
            $request = new PageRequest($page, $lang);
            $controller->render($request);
            echo "✓ Cached: {$lang}/{$page->value}\n";
        } catch (Exception $e) {
            echo "✗ Failed: {$lang}/{$page->value} - {$e->getMessage()}\n";
        }
    }
}

echo "Cache warming completed!\n";