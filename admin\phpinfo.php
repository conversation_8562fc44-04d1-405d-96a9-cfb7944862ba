<?php
$allowedHosts = ['127.0.0.1', '::1', 'localhost'];
$allowedDomains = ['l2gve.local', 'localhost'];

$isAllowed = in_array($_SERVER['REMOTE_ADDR'], $allowedHosts) || 
             in_array($_SERVER['HTTP_HOST'], $allowedDomains);

if (!$isAllowed) {
    die('Access denied');
}

echo "<h2>PHP Information</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Match expression support:</strong> " . (version_compare(PHP_VERSION, '8.0.0', '>=') ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>str_contains support:</strong> " . (function_exists('str_contains') ? '✅ Yes' : '❌ No') . "</p>";

echo "<hr>";
phpinfo();