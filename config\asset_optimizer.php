<?php
declare(strict_types=1);

class AssetOptimizer {
    public function __construct(
        private readonly CacheInterface $cache = CacheManager::getInstance()
    ) {}
    
    public function optimizeCSS(array $files): string {
        $cacheKey = 'css_' . md5(implode('|', $files));
        
        $cached = $this->cache->get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        $combined = '';
        foreach ($files as $file) {
            if (file_exists($file)) {
                $css = file_get_contents($file);
                $css = $this->minifyCSS($css);
                $combined .= $css . "\n";
            }
        }
        
        $this->cache->set($cacheKey, $combined, 86400); // 24 hours
        return $combined;
    }
    
    public function optimizeJS(array $files): string {
        $cacheKey = 'js_' . md5(implode('|', $files));
        
        $cached = $this->cache->get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        $combined = '';
        foreach ($files as $file) {
            if (file_exists($file)) {
                $js = file_get_contents($file);
                $combined .= $js . ";\n";
            }
        }
        
        $this->cache->set($cacheKey, $combined, 86400);
        return $combined;
    }
    
    private function minifyCSS(string $css): string {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t", '  ', '    ', '    '], '', $css);
        
        return $css;
    }
}