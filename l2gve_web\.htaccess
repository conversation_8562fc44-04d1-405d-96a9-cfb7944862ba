# PHP 8.4 optimizations
php_value opcache.enable 1
php_value opcache.jit_buffer_size 256M
php_value opcache.jit 1255
php_value opcache.memory_consumption 256
php_value opcache.max_accelerated_files 20000
php_value opcache.validate_timestamps 0

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain text/html text/xml text/css
    AddOutputFilterByType DEFLATE application/xml application/xhtml+xml application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript application/x-javascript
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Cache headers
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# ETags
FileETag MTime Size

# Rewrite rules
RewriteEngine On

# Route all pages through PHP
RewriteRule ^en/wiki$ wiki.php [L]
RewriteRule ^vn/wiki$ wiki.php [L]
RewriteRule ^en/$ index.php [L]
RewriteRule ^vn/$ index.php [L]

# API routes
RewriteRule ^api/(.*)$ api/$1 [L]

# Admin routes  
RewriteRule ^admin/(.*)$ admin/$1 [L]

# Force redirect root to /en
RewriteRule ^$ /en [R=301,L]
RewriteRule ^index\.php$ /en [R=301,L]
RewriteRule ^index\.html$ /en [R=301,L]









