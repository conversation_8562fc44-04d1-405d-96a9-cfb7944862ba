<?php
declare(strict_types=1);

// PHP 8.4 Advanced Preloading configuration for optimal performance
// This file should be loaded via opcache.preload in php.ini

if (!function_exists('opcache_compile_file')) {
    echo "OPcache not available, preloading disabled\n";
    return;
}

// Base directory
$baseDir = dirname(__DIR__);

// Core files to preload (in dependency order)
$preloadFiles = [
    // Interfaces first
    $baseDir . '/config/cache_manager.php', // Contains CacheInterface

    // Configuration files
    $baseDir . '/config/autoload.php',
    $baseDir . '/config/database.php',
    $baseDir . '/config/query_builder.php',
    $baseDir . '/config/asset_optimizer.php',
    $baseDir . '/config/asset_manager.php',
    $baseDir . '/config/page_controller.php',

    // Model files (if they exist)
    $baseDir . '/models/User.php',
    $baseDir . '/models/News.php',
    $baseDir . '/models/BaseModel.php',

    // API controllers
    $baseDir . '/api/news.php',

    // Template files
    $baseDir . '/template/optimized_head.php',
];

// Additional directories to scan for PHP files
$scanDirectories = [
    $baseDir . '/config',
    $baseDir . '/models',
    $baseDir . '/api',
];

// Performance monitoring
$startTime = microtime(true);
$preloadedCount = 0;
$failedCount = 0;

// Function to safely preload files
function preloadFile(string $file): bool {
    global $preloadedCount, $failedCount;

    if (!file_exists($file)) {
        echo "✗ File not found: " . basename($file) . "\n";
        $failedCount++;
        return false;
    }

    try {
        // Check if file is already compiled
        $status = opcache_get_status();
        $realPath = realpath($file);

        if (isset($status['scripts'][$realPath])) {
            echo "↻ Already compiled: " . basename($file) . "\n";
            return true;
        }

        opcache_compile_file($file);
        $preloadedCount++;
        echo "✓ Preloaded: " . basename($file) . " (" . number_format(filesize($file)) . " bytes)\n";
        return true;

    } catch (Throwable $e) {
        echo "✗ Failed to preload: " . basename($file) . " - " . $e->getMessage() . "\n";
        $failedCount++;
        return false;
    }
}

// Function to scan directory for PHP files
function scanForPhpFiles(string $directory): array {
    $files = [];
    if (!is_dir($directory)) {
        return $files;
    }

    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );

    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $files[] = $file->getRealPath();
        }
    }

    return $files;
}

echo "Starting PHP 8.4 Advanced Preload Process...\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "OPcache Version: " . phpversion('Zend OPcache') . "\n";
echo "JIT Status: " . (ini_get('opcache.jit') ? 'Enabled (' . ini_get('opcache.jit') . ')' : 'Disabled') . "\n";
echo str_repeat('-', 60) . "\n";

// Preload core files first
echo "Preloading core files...\n";
foreach ($preloadFiles as $file) {
    preloadFile($file);
}

// Scan additional directories
echo "\nScanning additional directories...\n";
foreach ($scanDirectories as $directory) {
    if (is_dir($directory)) {
        echo "Scanning: $directory\n";
        $files = scanForPhpFiles($directory);
        foreach ($files as $file) {
            // Skip files already processed
            if (!in_array($file, $preloadFiles)) {
                preloadFile($file);
            }
        }
    }
}

// Preload commonly used classes and check availability
echo "\nChecking system classes...\n";
$systemClasses = [
    'PDO', 'PDOStatement', 'PDOException',
    'DateTime', 'DateTimeImmutable', 'DateTimeZone',
    'Exception', 'RuntimeException', 'InvalidArgumentException',
    'JsonException', 'TypeError', 'ValueError',
    'SplFileInfo', 'RecursiveDirectoryIterator', 'RecursiveIteratorIterator',
    'Redis' // If Redis extension is loaded
];

foreach ($systemClasses as $class) {
    if (class_exists($class) || interface_exists($class)) {
        echo "✓ Available: $class\n";
    } else {
        echo "✗ Not available: $class\n";
    }
}

// Performance summary
$endTime = microtime(true);
$duration = round(($endTime - $startTime) * 1000, 2);

echo "\n" . str_repeat('=', 60) . "\n";
echo "Preload Summary:\n";
echo "✓ Successfully preloaded: $preloadedCount files\n";
echo "✗ Failed to preload: $failedCount files\n";
echo "⏱ Total time: {$duration}ms\n";

// OPcache statistics
$opcacheStatus = opcache_get_status();
if ($opcacheStatus) {
    echo "📊 OPcache memory usage: " . number_format($opcacheStatus['memory_usage']['used_memory']) . " bytes\n";
    echo "📊 Cached scripts: " . count($opcacheStatus['scripts']) . "\n";
    echo "📊 Hit rate: " . round($opcacheStatus['opcache_statistics']['opcache_hit_rate'], 2) . "%\n";
}

echo "Preload process completed!\n";
