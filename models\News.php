<?php
class News {
    private $conn;
    private $table_name = "news";

    public $id;
    public $title;
    public $description;
    public $content;
    public $image;
    public $type;
    public $status;
    public $created_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Lấy tin tức theo ID
    public function getById() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id AND status = 'active'";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        $stmt->execute();
        return $stmt;
    }

    // Lấy tất cả tin tức
    public function getAll($type = null) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE status = 'active'";
        
        if ($type && $type != 'all') {
            $query .= " AND type = :type";
        }
        
        $query .= " ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        
        if ($type && $type != 'all') {
            $stmt->bindParam(':type', $type);
        }
        
        $stmt->execute();
        return $stmt;
    }

    // Tạo tin tức mới
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET title=:title, description=:description, content=:content, 
                      image=:image, type=:type, status=:status";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':content', $this->content);
        $stmt->bindParam(':image', $this->image);
        $stmt->bindParam(':type', $this->type);
        $stmt->bindParam(':status', $this->status);

        return $stmt->execute();
    }

    // Cập nhật tin tức
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET title=:title, description=:description, content=:content, 
                      image=:image, type=:type, status=:status 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':content', $this->content);
        $stmt->bindParam(':image', $this->image);
        $stmt->bindParam(':type', $this->type);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Xóa tin tức
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        return $stmt->execute();
    }
}
?>

