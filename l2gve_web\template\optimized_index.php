<?php
declare(strict_types=1);

require_once __DIR__ . '/../config/autoload.php';
require_once __DIR__ . '/optimized_head.php';

/**
 * Optimized Index Template for L2GVE
 * Uses PHP 8.4 features and performance optimizations
 */
function renderOptimizedIndex(string $language = 'en'): string {
    $title = match($language) {
        'vn' => 'L2GVE - Essence GvE x100 - Máy chủ Lineage II tốt nhất',
        default => 'L2GVE - Essence GvE x100 - Best Lineage II Server'
    };
    
    $description = match($language) {
        'vn' => 'Tham gia thế giới Lineage II với máy chủ L2GVE Essence GvE x100. Trải nghiệm PvP tuyệt vời, tính năng độc đáo và cộng đồng sôi động.',
        default => 'Dive into the world of Lineage II with the L2GVE Essence GvE x100 server. Experience amazing PvP, unique features, and vibrant community.'
    };
    
    // Start output buffering for performance
    ob_start();
    
    // <PERSON>der optimized head
    echo renderOptimizedHead($title, $description, $language);
    ?>

<body>
    <!-- GTM noscript -->
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T99S9NM7" 
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>

    <!-- Optimized preloader -->
    <div class="preload" id="preloader">
        <div class="preload__progress" data-preload-progress></div>
        <img src="../template/site/l2gve/images/preload/preload.png" 
             alt="Loading..." 
             class="preload__logo" 
             style="width: 9rem"
             loading="eager" />
        <div class="preload__items">
            <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" loading="eager" />
            <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" loading="eager" />
            <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" loading="eager" />
        </div>
    </div>

    <!-- Main page content -->
    <div class="page" id="mainPage" style="opacity: 0;">
        <!-- Background with lazy loading -->
        <div class="bg">
            <img src="../template/site/l2gve/images/bg/bg-full.jpg" 
                 alt="L2GVE Background" 
                 class="bg__img" 
                 aria-hidden="true"
                 loading="eager"
                 decoding="async" />
        </div>

        <!-- Navigation section -->
        <div class="section compensate-for-scrollbar" data-section="navigation">
            <nav class="navigation" data-container="navigation">
                <!-- Navigation content will be loaded here -->
                <div class="navigation__content lazy-load">
                    <!-- Navigation items -->
                </div>
            </nav>
        </div>

        <!-- Header section -->
        <header class="section" data-section="header" data-target-section="header" data-target-section-offset="0" id="header">
            <div class="container" data-container="header">
                <div class="header">
                    <div class="header__box">
                        <!-- Countdown -->
                        <div class="header__countdown lazy-load" data-place-from="countdown">
                            <div class="countdown countdown_pos" data-countdown data-place-container="countdown">
                                <div class="countdown__heading">
                                    <?= $language === 'vn' ? 'Thời gian đến khi OBT bắt đầu:' : 'Time until OBT starts:' ?>
                                </div>
                                <!-- Countdown content -->
                            </div>
                        </div>

                        <!-- Main title -->
                        <div class="header__title fade-in">
                            <h1><?= $language === 'vn' ? 'L2GVE - Essence GvE x100' : 'L2GVE - Essence GvE x100' ?></h1>
                            <p class="header__subtitle">
                                <?= $language === 'vn' ? 'Máy chủ Lineage II tốt nhất' : 'The Ultimate Lineage II Experience' ?>
                            </p>
                        </div>

                        <!-- Action buttons -->
                        <div class="header__actions fade-in">
                            <a href="#download" class="btn btn-primary">
                                <?= $language === 'vn' ? '🎮 Tải Game' : '🎮 Download Game' ?>
                            </a>
                            <a href="#register" class="btn btn-secondary">
                                <?= $language === 'vn' ? '📝 Đăng Ký' : '📝 Register' ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Features section -->
        <section class="section lazy-load" data-section="features" id="features">
            <div class="container">
                <h2 class="section__title">
                    <?= $language === 'vn' ? '🌟 Tính Năng Nổi Bật' : '🌟 Amazing Features' ?>
                </h2>
                
                <div class="features__grid">
                    <div class="feature__item">
                        <div class="feature__icon">⚔️</div>
                        <h3><?= $language === 'vn' ? 'PvP Cân Bằng' : 'Balanced PvP' ?></h3>
                        <p><?= $language === 'vn' ? 'Hệ thống PvP được cân bằng hoàn hảo' : 'Perfectly balanced PvP system' ?></p>
                    </div>
                    
                    <div class="feature__item">
                        <div class="feature__icon">🏆</div>
                        <h3><?= $language === 'vn' ? 'Sự Kiện Độc Đáo' : 'Unique Events' ?></h3>
                        <p><?= $language === 'vn' ? 'Các sự kiện thú vị diễn ra hàng ngày' : 'Exciting events happening daily' ?></p>
                    </div>
                    
                    <div class="feature__item">
                        <div class="feature__icon">👥</div>
                        <h3><?= $language === 'vn' ? 'Cộng Đồng Sôi Động' : 'Active Community' ?></h3>
                        <p><?= $language === 'vn' ? 'Tham gia cộng đồng game thủ đông đảo' : 'Join our vibrant gaming community' ?></p>
                    </div>
                </div>
            </div>
        </section>

        <!-- News section -->
        <section class="section lazy-load" data-section="news" id="news">
            <div class="container">
                <h2 class="section__title">
                    <?= $language === 'vn' ? '📰 Tin Tức Mới Nhất' : '📰 Latest News' ?>
                </h2>
                
                <div class="news__container" id="newsContainer">
                    <!-- News will be loaded via AJAX for better performance -->
                    <div class="loading-placeholder">
                        <?= $language === 'vn' ? 'Đang tải tin tức...' : 'Loading news...' ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- Community section -->
        <section class="section lazy-load" data-section="community" id="community">
            <div class="container">
                <h2 class="section__title">
                    <?= $language === 'vn' ? '🌐 Tham Gia Cộng Đồng' : '🌐 Join Our Community' ?>
                </h2>
                
                <div class="community__links">
                    <a href="#discord" class="community__link">
                        <span class="community__icon">💬</span>
                        Discord
                    </a>
                    <a href="#facebook" class="community__link">
                        <span class="community__icon">📘</span>
                        Facebook
                    </a>
                    <a href="#youtube" class="community__link">
                        <span class="community__icon">📺</span>
                        YouTube
                    </a>
                </div>
            </div>
        </section>
    </div>

    <!-- Back to top button -->
    <div id="backToTop" class="back-to-top" style="display: none;">
        <i class="arrow-up"></i>
    </div>

    <!-- Performance optimized scripts -->
    <script>
    // Critical inline JavaScript for immediate functionality
    (function() {
        'use strict';
        
        // Performance monitoring
        const perfStart = performance.now();
        
        // Preloader management
        const preloader = document.getElementById('preloader');
        const mainPage = document.getElementById('mainPage');
        
        // Minimum preload time for smooth UX
        const minPreloadTime = window.__config?.preload?.minTime * 1000 || 1000;
        
        // Hide preloader and show main content
        function hidePreloader() {
            if (preloader) {
                preloader.style.opacity = '0';
                preloader.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    preloader.style.display = 'none';
                    if (mainPage) {
                        mainPage.style.opacity = '1';
                        mainPage.style.transition = 'opacity 0.5s ease';
                    }
                }, 500);
            }
        }
        
        // Lazy loading implementation
        function initLazyLoading() {
            const lazyElements = document.querySelectorAll('.lazy-load');
            
            if ('IntersectionObserver' in window) {
                const lazyObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('loaded');
                            lazyObserver.unobserve(entry.target);
                        }
                    });
                }, {
                    rootMargin: '50px 0px',
                    threshold: 0.1
                });
                
                lazyElements.forEach(el => lazyObserver.observe(el));
            } else {
                // Fallback for older browsers
                lazyElements.forEach(el => el.classList.add('loaded'));
            }
        }
        
        // Load news via AJAX
        function loadNews() {
            const newsContainer = document.getElementById('newsContainer');
            if (!newsContainer) return;
            
            fetch('/api/news.php?limit=5&lang=<?= $language ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.news) {
                        newsContainer.innerHTML = data.news.map(item => `
                            <article class="news__item">
                                <h3>${item.title}</h3>
                                <p>${item.excerpt}</p>
                                <time>${item.date}</time>
                            </article>
                        `).join('');
                    } else {
                        newsContainer.innerHTML = '<p><?= $language === 'vn' ? 'Không có tin tức mới' : 'No news available' ?></p>';
                    }
                })
                .catch(() => {
                    newsContainer.innerHTML = '<p><?= $language === 'vn' ? 'Lỗi tải tin tức' : 'Error loading news' ?></p>';
                });
        }
        
        // Back to top functionality
        function initBackToTop() {
            const backToTop = document.getElementById('backToTop');
            if (!backToTop) return;
            
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTop.style.display = 'block';
                } else {
                    backToTop.style.display = 'none';
                }
            });
            
            backToTop.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }
        
        // Initialize everything when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const loadTime = performance.now() - perfStart;
            const remainingTime = Math.max(0, minPreloadTime - loadTime);
            
            setTimeout(() => {
                hidePreloader();
                initLazyLoading();
                loadNews();
                initBackToTop();
                
                // Performance logging
                if (window.__config?.performance?.logging) {
                    console.log('Page loaded in:', loadTime.toFixed(2), 'ms');
                }
            }, remainingTime);
        });
        
        // Service Worker registration
        if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(reg => console.log('SW registered'))
                    .catch(err => console.log('SW registration failed'));
            });
        }
    })();
    </script>

    <?= renderOptimizedFooter() ?>
</body>
</html>

    <?php
    return ob_get_clean();
}

// Usage example
if (php_sapi_name() !== 'cli') {
    $language = str_contains($_SERVER['REQUEST_URI'], '/vn/') ? 'vn' : 'en';
    echo renderOptimizedIndex($language);
}
