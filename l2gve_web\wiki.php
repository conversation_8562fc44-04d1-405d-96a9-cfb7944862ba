<?php
declare(strict_types=1);

require_once 'config/autoload.php';
require_once 'config/page_controller.php';

// Detect language from URL - Compatible with PHP 7.x
$language = 'en'; // default
if (str_contains($_SERVER['REQUEST_URI'], '/vn/')) {
    $language = 'vn';
} elseif (str_contains($_SERVER['REQUEST_URI'], '/en/')) {
    $language = 'en';
}

try {
    $controller = new PageController();
    $request = new PageRequest(PageType::WIKI, $language);
    
    // Set cache headers
    header('Cache-Control: public, max-age=3600');
    header('ETag: "' . md5($request->language . $request->type->value) . '"');
    
    echo $controller->render($request);
    
} catch (Exception $e) {
    error_log("Wiki error: " . $e->getMessage());
    http_response_code(500);
    echo "Page temporarily unavailable";
}
