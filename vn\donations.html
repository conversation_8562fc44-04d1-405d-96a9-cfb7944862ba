<!doctype html>
    <!--[if lte IE 9]><html lang="en" class="lt-ie10 lt-ie10-msg no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html lang="en" class="no-focus"> <!--<![endif]-->
<head lang="en">
    <title>L2GVE.COM - Account Panel for Interlude</title><meta property="og: title" content="L2GVE.COM - Account Panel for Interlude" ><meta property="og: site_name" content="% site_name%" ><meta property="og: type" content="website" ><meta property="og: url" content="http://l2gve.com/en/donations/interlude.824" ><meta name="description" content="L2GVE.COM - Account Panel for Interlude!" ><meta property="og: description" content="L2GVE.COM - Account Panel for Interlude!" ><meta property="twitter: description" content="% site_name% Account Panel for Interlude!" ><meta name="keywords" content="mmoweb, mmoweb4" ><meta charset="utf-8" ><meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" ><meta property="og:image" content="/template/panel/assets/media/favicon/apple-touch-icon-180x180.png" ><link rel="shortcut icon" href="../template/panel/assets/media/favicon/favicon.ico" ><link rel="icon" type="image/png" sizes="180x180" href="../template/panel/assets/media/favicon/apple-touch-icon-180x180.png" ><link rel="icon" type="image/png" sizes="16x16" href="../template/panel/assets/media/favicon/favicon-16x16.png" ><link rel="apple-touch-icon" sizes="32x32" href="../template/panel/assets/media/favicon/favicon-32x32.png" ><link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Muli:300,400,400i,600,700" ><link id="css-main" rel="stylesheet" href="../template/panel/assets/css/codebase.css@v=**********.css" ><link rel="stylesheet" href="../template/panel/assets/css/custom.css@v=1678148040.css" ><script src="https://www.googletagmanager.com/gtag/js?id=UA-167385217-1" async="true" ></script><script>window.dataLayer = window.dataLayer || []; 
            function gtag(){dataLayer.push(arguments);} 
            gtag('js', new Date()); gtag('config', 'UA-167385217-1',{ 'anonymize_ip': true });</script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-T99S9NM7');</script><script>   (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
               m[i].l=1*new Date();k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
               (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");
               ym(97762192, "init", {
                    clickmap:true,
                    trackLinks:true,
                    accurateTrackBounce:true,
                    webvisor:true
               });</script><noscript><div><img src="https://mc.yandex.ru/watch/97762192" style="position:absolute; left:-9999px;" alt="" /></div></noscript><link rel="stylesheet" href="../template/panel/assets/js/plugins/ion-rangeslider/css/ion.rangeSlider.css" >    <!-- Stylesheets -->
                    <!-- END Stylesheets -->

        <link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
<link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />
    <link rel="stylesheet" href="../template/panel/assets/css/gwstyle.css@v=1714918634.css">
    </head>
    <body>
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T99S9NM7" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>        <!-- Page Container-->
            <div id="page-container" class="sidebar-inverse side-scroll main-content-boxed enable-page-overlay side-trans-enabled ">
            <!-- Sidebar -->
<nav id="sidebar">
    <!-- Sidebar Scroll Container -->
    <div id="sidebar-scroll">
        <!-- Sidebar Content -->
        <div class="sidebar-content">
            <!-- Side Header -->
            <div class="content-header content-header-fullrow bg-black-op-10">
                <div class="content-header-section text-center align-parent">
                    <button type="button" class="btn btn-circle btn-dual-secondary d-lg-none align-v-r" data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times text-danger"></i>
                    </button>
                    <!-- END Close Sidebar -->

                    <!-- Logo -->
                    <div class="content-header-item">
                        <a class="link-effect font-w700" href="index.html">
                            <span class="font-size-xl text-dual-primary-dark">L2GVE.COM</span>
                        </a>
                    </div>
                    <!-- END Logo -->
                </div>
            </div>
            <!-- END Side Header -->

            <!-- Side Main Navigation -->
            <div class="content-side content-side-full">

                <ul class="nav-main">
                    <button type="button" class="btn btn-block btn-alt-success btn-rounded dropdown-toggle push d-flex align-items-center justify-content-between min-width-175" id="toolbarDrop" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Lineage2"><img class="img-avatar img-avatar16 img-avatar-thumb m-0" src="../template/panel/assets/media/icon_platform/lineage2.png" alt="lineage2">Interlude [x30]</button><div class="dropdown-menu" aria-labelledby="toolbarDrop"><div class="ml-10 "><a class="dropdown-item " href="donations.html">Interlude [x30]</a></div></div>                    <li class=""><a href="rating.html" target="" lv_menu="1" class=" " ><i class="fa fa-line-chart"></i><span class="sidebar-mini-hide">Rankings</span></a></li><li class="open"><a href="donations.html" target="" lv_menu="300" class="active " ><i class="si si-diamond"></i><span class="sidebar-mini-hide">Donations</span></a></li><li class=""><a href="../index.html" target="_blank" lv_menu="500" class=" " ><i class="si si-action-undo"></i><span class="sidebar-mini-hide">Back to the main site</span></a></li>                </ul>
            </div>
            <!-- END Side Main Navigation -->
        </div>
        <!-- Sidebar Content -->
    </div>
    <!-- END Sidebar Scroll Container -->
</nav>
<!-- END Sidebar -->
<!-- Header -->
<header id="page-header">
    <!-- Header Content -->
    <div class="content-header">
        <!-- Left Section -->
        <div class="content-header-section">

                        <div class="content-header-item">

                <ul class="nav-main-header">
                    <button type="button" class="btn btn-block btn-alt-success btn-rounded dropdown-toggle push d-flex align-items-center justify-content-between min-width-175" id="toolbarDrop" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Lineage2"><img class="img-avatar img-avatar16 img-avatar-thumb m-0" src="../template/panel/assets/media/icon_platform/lineage2.png" alt="lineage2">Interlude [x30]</button><div class="dropdown-menu" aria-labelledby="toolbarDrop"><div class="ml-10 "><a class="dropdown-item " href="donations.html">Interlude [x30]</a></div></div>                </ul>
            </div>
        </div>
        <!-- END Left Section -->

        <!-- Middle Section -->
        <div class="content-header-section d-none d-lg-block">
            <ul class="nav-main-header">
                <li class=""><a href="rating.html" target="" lv_menu="1" class=" " ><i class="fa fa-line-chart"></i><span class="sidebar-mini-hide">Rankings</span></a></li><li class="open"><a href="donations.html" target="" lv_menu="300" class="active " ><i class="si si-diamond"></i><span class="sidebar-mini-hide">Donations</span></a></li><li class=""><a href="../index.html" target="_blank" lv_menu="500" class=" " ><i class="si si-action-undo"></i><span class="sidebar-mini-hide">Back to the main site</span></a></li>            </ul>
            <!-- END Header Navigation -->
        </div>
        <!-- END Middle Section -->

        <!-- Right Section -->
        <div class="content-header-section">


            

                <a href="sign-in.html" class="btn btn-dual-secondary mr-5 ">
                    <i class="si si-login"></i> Sign Ιn                </a>
                <a href="sign-up.html" class="btn btn-dual-secondary ">
                    <i class="fa fa-user-plus"></i> Register                </a>
            
            <!-- Toggle Sidebar -->
            <button type="button" class="btn btn-circle btn-dual-secondary d-lg-none" data-toggle="layout" data-action="sidebar_toggle">
                <i class="fa fa-navicon"></i>
            </button>
            <!-- END Toggle Sidebar -->
        </div>
        <!-- END Right Section -->
    </div>
    <!-- END Header Content -->

        <!-- Header Loader -->
        <div id="page-header-loader" class="overlay-header bg-primary">
            <div class="content-header content-header-fullrow text-center">
                <div class="content-header-item">
                    <i class="fa fa-sun-o fa-spin text-white"></i>
                </div>
            </div>
        </div>
        <!-- END Header Loader -->

</header>
<!-- END Header -->            <!-- Main Container -->
            <main id="main-container">
                                                    <div class="content content-full">
                        <div class="my-10 text-center">
    <h2 class="font-w700 text-black mb-10">Interlude x30</h2>
    <h3 class="h5 text-muted mb-0">Top-up Balance master account</h3>
</div>
<div class="row">
    <div class="col-lg-8 offset-lg-2">
        <div class="block block-rounded block-fx-shadow">
            <form action="https://l2gve.com/input" method="post" onsubmit="return false;">
                <input type="hidden" name="module_form" value="Modules\Globals\Donations\Donations"><input type="hidden" name="module" value="checkout_no_auth">                <div class="block-content pl-50 pr-50">
                    <div class="row">
                            <div class="col-md-12">
                                                                    <p class="alert alert-warning font-w600 text-center" style="border-radius: 3px;">
                                        Game currency for this server is not configured yet!                                    </p>
                                
                            </div>
                            <div class="col-md-12">
                                <div class="form-group  mb-20">
                                    <input type="text" id="sum_slider" data-grid="true">
                                </div>
                            </div>
                            <div class="col-md-12" id="calculation_board">
                                                                    <div class="row border-bottom pt-5">
                                        <div class="col-3">
                                            USD                                        </div>
                                        <div class="col-9">
                                            <span class="pull-right"><span id="sum_USD">0</span> </span>
                                        </div>
                                    </div>
                                                                    <div class="row border-bottom pt-5">
                                        <div class="col-3">
                                            RUB                                        </div>
                                        <div class="col-9">
                                            <span class="pull-right"><span id="sum_RUB">0</span> </span>
                                        </div>
                                    </div>
                                                                    <div class="row border-bottom pt-5">
                                        <div class="col-3">
                                            EUR                                        </div>
                                        <div class="col-9">
                                            <span class="pull-right"><span id="sum_EUR">0</span> </span>
                                        </div>
                                    </div>
                                                                    <div class="row border-bottom pt-5">
                                        <div class="col-3">
                                            UAH                                        </div>
                                        <div class="col-9">
                                            <span class="pull-right"><span id="sum_UAH">0</span> </span>
                                        </div>
                                    </div>
                                                            </div>
                            <div class="col-md-6 border-left" id="item_board" style="display: none;">

                            </div>
                            <div class="col-md-12">
                                <div class="form-group mt-20">
                                    <label for="payment_method">Choose a payment method:</label>
                                </div>
                                <div class="row gutters-tiny mt-20">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>
                            </div>

                                                </div>
                    </div>
                <div class="block-content block-content-sm block-content-full bg-body-light text-center mt-20">
                    <button type="submit" class="btn btn-alt-primary submit-form">
                        <i class="fa fa-money mr-5"></i> Top-up                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    document.addEventListener("DOMContentLoaded", function (event) {
        $("#sum_slider").ionRangeSlider({
            min: 5,
            max: 10000,
            from: 50,
            grid: true,
            postfix: ' ',
            skin: 'round',
            prettify: function (num) {
                let bonus = 0;
                let bonus_item = [];
                let bonus_item_key = [];
                let bonus_item_html = '<div class="row border-bottom text-center pt-5" style="border-bottom-style: dashed !important;"><div class="col-12">Gift items</div></div>';
                let bonus_item_show = false;
                let payment_method = $("input[name='payment_method']:checked"). val();
                let price = parseFloat($("input[name='type_id']:checked").data('price'));

                
                
                $.each( bonus_item_key, function( key, value ) {
                    if (value !== "undefined"){
                        if (typeof bonus_item[key] !== "undefined") {
                            if (typeof bonus_item[key][value] !== "undefined") {
                                bonus_item_show = true;
                                $.each( bonus_item[key][value], function( idx, item ) {
                                    bonus_item_html += '<div class="row border-bottom pt-5"><div class="col-10"><img src="https://l2gve.com/en/donations/'+item.icon+'" width="15px"> '+item.name+' '+item.add_name+' '+(item.enc > 0 ? '<span style="color: #bbb529">'+item.enc+'</span>' : '')+' </div><div class="col-2"><span class="pull-right"><span>'+item.count+'</span>x</span></div></div>';
                                });

                            }
                        }
                    }
                });

                if (bonus_item_show){
                    $('#calculation_board').removeClass('col-md-12').addClass("col-md-6");
                    $('#item_board').html(bonus_item_html);
                    $('#item_board').show();
                }else{
                    $('#calculation_board').removeClass('col-md-6').addClass("col-md-12");
                    $('#item_board').html('');
                    $('#item_board').hide();
                }


                /*calculation_board item_board*/

                if(bonus > 0){
                    $('#bonus_sum').html('+'+ Math.floor(bonus / price) + ' Bonus');
                    return num+", "+"+"+Math.floor(bonus / price)+" ";
                }else{
                    $('#bonus_sum').html('+'+ 0);
                    return num;
                }

            },
            onChange: function (data) {
                $('#coin').val(data.from);
                changeSum(data.from,true);
            }
        });
        var sum_slider = $("#sum_slider").data("ionRangeSlider");

        window.changeSum = function(sum,slider){

            let price = $("input[name='type_id']:checked").data('price');

            var sum_usd = Math.round(((sum*(1*parseFloat(price)))*1)*100)/100;

            $('.sum').val(sum_usd);
            let oi = 0;
                        oi = findFirstNonZeroIndex('1');
            $('#sum_USD').html(Math.ceil(sum_usd*1*oi)/oi);
                        oi = findFirstNonZeroIndex('91.491308');
            $('#sum_RUB').html(Math.ceil(sum_usd*91.491308*oi)/oi);
                        oi = findFirstNonZeroIndex('0.928837');
            $('#sum_EUR').html(Math.ceil(sum_usd*0.928837*oi)/oi);
                        oi = findFirstNonZeroIndex('39.428204');
            $('#sum_UAH').html(Math.ceil(sum_usd*39.428204*oi)/oi);
                        $('#sum_USD').html(sum_usd);

            if(!slider){
                sum_slider.update({
                    from: sum
                });
            }
        };
        function findFirstNonZeroIndex(num) {
            let numberString = Number(num).toLocaleString('fullwide', { maximumSignificantDigits: 21 }).replace('.', '');
            let oi = Array.from(numberString).findIndex(i => i > 0);
            let pos = '10';
            for (var i = 0; i < oi; i++) {
                pos += '0';
            }
            pos = parseInt(pos);
            if (pos < 100) pos = 100;
            return parseInt(pos);
        }
        function initInGame() {
            let input = $("input[name='type_id']:checked");
            let message = input.data('message');
            let long_name = input.data('long-name');
            let short_name = input.data('short-name');
            let type = input.data('type');
            setTextInputInGame(type, message, long_name, short_name);
        }
        function setTextInputInGame(type, message, long_name, short_name) {

            if (type == 'account'){
                $('#recipient-label').html('Specify a game account ID');
                $('.type_icon').html('<i class="fa fa-user"></i>');
                $('#recipient').attr("placeholder", "XX_Login");
            }else{
                $('#recipient-label').html('Specify name of the character');
                $('.type_icon').html('<i class="fa fa-street-view"></i>');
                $('#recipient').attr("placeholder", "MegaMag");
            }

            $('.short_name_icon').html('<i class="fa fa-plus-square"></i>');
            if (short_name.length) {
                $('.short_name_icon').html(short_name);
                let slider = $("#sum_slider").data("ionRangeSlider");
                if(slider){
                    slider.update({
                        postfix: ' '+short_name,
                    });
                }
            }
            if (long_name.length)
                $('#coin').attr("placeholder", "Specify quantity " + long_name);

            $('.in-game-message').html('');
            if (message.length)
                $('.in-game-message').html(message.replace(/\n/g, '<br>'));


        }
        initInGame();


        $('body').on('change', "input[name='type_id']", function (e) {
            let type = $(this).data('type');
            let message = $(this).data('message');
            let long_name = $(this).data('long-name');
            let short_name = $(this).data('short-name');

            setTextInputInGame(type, message, long_name, short_name);


            window.changeSum(document.getElementById("coin").value,true);
        });

        window.changeSum(document.getElementById("coin").value,true);
    });
</script>                    </div>
                            </main>
            <!-- END Main Container -->
                                    <!-- Footer -->
<footer id="page-footer" class="opacity-0">
    <div class="content py-20 font-size-xs clearfix">


        <div class="float-right">
            <div class="form-material pt-0">
                <select class="form-control font-size-xs" id="change_lang" style="height: 30px;">
                                            <option hreflang="ru" value="/ru/donations/interlude.824" >RUS</option>
                                            <option hreflang="en" value="/en/donations/interlude.824" selected>ENG</option>
                                    </select>
            </div>
        </div>


        <div class="float-left" style="color: #ffffff17;">
            Back-End: MmoWeb <br>
            Front-End: <a href="https://get-web.site" target="_blank" style="color: #ffffff17;">Get-Web.site</a>
        </div>
    </div>
</footer>
<!-- END Footer -->                                <!-- Pop Out Modal -->
                <div class="modal fade" id="modal-ajax" tabindex="-1" role="dialog" aria-labelledby="modal-popout" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-popout" role="document">
                        <div class="modal-content">
                            <form class="modal-ajax-form" action="https://l2gve.com/input" method="post" onsubmit="return false;">
                                <div class="block block-themed block-transparent mb-0">
                                    <div class="block-header bg-primary-dark">
                                        <h3 class="block-title modal-ajax-title"></h3>
                                        <div class="block-options">
                                            <button type="button" class="btn-block-option" data-dismiss="modal" aria-label="Close">
                                                <i class="si si-close"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="block-content modal-ajax-content"></div>
                                </div>
                                <div class=" modal-ajax-footer block-content block-content-sm block-content-full bg-body-light block-settings-save-fix"></div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- END Pop Out Modal -->
        </div>
        <!-- END Page Container -->
        <!-- Core JS -->
        <script src="../template/panel/assets/js/codebase.core.min.js@v=**********" ></script><script src="../template/panel/assets/js/codebase.app.min.js@v=**********" ></script><script src="../template/panel/assets/js/plugins/bootstrap-notify/bootstrap-notify.min.js" ></script><script src="../template/panel/assets/js/plugins/bootstrap-history-tabs/bootstrap-history-tabs.js@v=2" ></script><script src="../template/panel/assets/js/plugins/masonry/masonry.pkgd.min.js" ></script><script src="../template/panel/assets/js/mmoweb.js@v=**********" ></script><script src="https://mmoweb.biz/watch.js" ></script><script>window.masonry_div = $('.grid').masonry({itemSelector: '.grid-item',columnWidth: '.grid-sizer',percentPosition: true});$('.nav-tabs a').historyTabs();</script><script src="../template/panel/assets/js/plugins/ion-rangeslider/js/ion.rangeSlider.min.js" ></script>    </body>
</html>