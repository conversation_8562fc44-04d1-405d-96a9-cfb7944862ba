<?php
declare(strict_types=1);

require_once '../config/database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

enum NewsType: string {
    case NEWS = 'news';
    case PROMOTIONS = 'promotions';
    case UPDATES = 'updates';
    case FEATURES = 'features';
    
    public function getDisplayName(): string {
        return match($this) {
            self::NEWS => 'News',
            self::PROMOTIONS => 'Promotions',
            self::UPDATES => 'Updates',
            self::FEATURES => 'Features'
        };
    }
}

readonly class NewsRequest {
    public function __construct(
        public string $title,
        public string $description,
        public NewsType $type,
        public ?string $image = null,
        public ?string $link = null
    ) {}
    
    public static function fromJson(string $json): self {
        $data = json_decode($json, flags: JSON_THROW_ON_ERROR);
        
        return new self(
            title: $data->title ?? throw new InvalidArgumentException('Title is required'),
            description: $data->description ?? throw new InvalidArgumentException('Description is required'),
            type: NewsType::from($data->type ?? throw new InvalidArgumentException('Type is required')),
            image: $data->image ?? null,
            link: $data->link ?? null
        );
    }
}

class NewsController {
    public function __construct(
        private readonly Database $db = new Database()
    ) {}
    
    public function handleRequest(): void {
        try {
            match($_SERVER['REQUEST_METHOD']) {
                'GET' => $this->handleGet(),
                'POST' => $this->handlePost(),
                'PUT' => $this->handlePut(),
                'DELETE' => $this->handleDelete(),
                default => throw new BadMethodCallException('Method not allowed')
            };
        } catch (Exception $e) {
            http_response_code(match($e::class) {
                InvalidArgumentException::class => 400,
                BadMethodCallException::class => 405,
                default => 500
            });
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    private function handleGet(): void {
        $conn = $this->db->getConnection();
        $stmt = $conn->query("SELECT * FROM news ORDER BY created_at DESC");
        echo json_encode($stmt->fetchAll());
    }
    
    private function handlePost(): void {
        $request = NewsRequest::fromJson(file_get_contents("php://input"));
        $conn = $this->db->getConnection();
        
        $stmt = $conn->prepare("INSERT INTO news (title, description, type, image, link, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([$request->title, $request->description, $request->type->value, $request->image, $request->link]);
        
        echo json_encode(['success' => true, 'id' => $conn->lastInsertId()]);
    }
}

(new NewsController())->handleRequest();

