<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chi tiết tin tức - L2GVE</title>
    
    <!-- CSS links -->
    <link rel="stylesheet" href="../template/site/l2gve/css/main.css@v=1719174774.css">
    <link rel="stylesheet" href="../template/site/l2gve/css/custom.css@v=1714752167.css">
</head>
<body class="body">
    <div class="page">
        <section class="section" data-section="news-detail">
            <div class="container">
                <div class="news-detail">
                    <div class="navigation-height-compensate"></div>
                    
                    <div class="back-wrap">
                        <a href="/vn/" class="back">
                            <i class="gwi gwi_left"></i> Về trang chủ
                        </a>
                    </div>
                    
                    <div class="news-detail__content">
                        <div class="news-detail__image">
                            <img id="newsImage" src="" alt="Hình ảnh tin tức">
                        </div>
                        
                        <div class="news-detail__info">
                            <div class="news-detail__type" id="newsType"></div>
                            <div class="news-detail__date" id="newsDate"></div>
                        </div>
                        
                        <h1 class="news-detail__title" id="newsTitle"></h1>
                        
                        <div class="news-detail__description" id="newsDescription"></div>
                        
                        <div class="news-detail__content-full" id="newsContent"></div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <style>
        .news-detail {
            padding: 100px 0 50px;
            color: #fff;
        }
        
        .news-detail__image img {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .news-detail__info {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .news-detail__type {
            background: #007cba;
            color: #fff;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 14px;
        }
        
        .news-detail__date {
            color: #999;
            font-size: 14px;
        }
        
        .news-detail__title {
            font-size: 32px;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .news-detail__description {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #ddd;
        }
        
        .news-detail__content-full {
            font-size: 16px;
            line-height: 1.8;
            color: #ccc;
        }
        
        .back {
            display: inline-flex;
            align-items: center;
            color: #007cba;
            text-decoration: none;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .back:hover {
            color: #fff;
        }
        
        .gwi_left::before {
            content: "←";
            margin-right: 10px;
        }
    </style>

    <script>
        // Lấy ID tin tức từ URL
        const urlParams = new URLSearchParams(window.location.search);
        const newsId = urlParams.get('id');
        
        if (newsId) {
            // Fetch chi tiết tin tức
            fetch(`../api/news.php?id=${newsId}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.querySelector('.news-detail__content').innerHTML = 
                        '<h2>Không tìm thấy tin tức</h2><p>Bài viết tin tức được yêu cầu không thể tìm thấy.</p>';
                    return;
                }
                
                // Hiển thị dữ liệu
                document.getElementById('newsImage').src = data.img || '../template/site/l2gve/images/post/img-def-0.jpg';
                document.getElementById('newsType').textContent = data.type;
                document.getElementById('newsDate').textContent = data.date;
                document.getElementById('newsTitle').textContent = data.title;
                document.getElementById('newsDescription').textContent = data.desc;
                document.getElementById('newsContent').innerHTML = data.content || data.desc;
                
                // Cập nhật title trang
                document.title = data.title + ' - L2GVE';
            })
            .catch(error => {
                console.error('Error:', error);
                document.querySelector('.news-detail__content').innerHTML = 
                    '<h2>Lỗi</h2><p>Không thể tải bài viết tin tức.</p>';
            });
        } else {
            document.querySelector('.news-detail__content').innerHTML = 
                '<h2>Chưa chọn tin tức</h2><p>Vui lòng chọn một bài viết tin tức để đọc.</p>';
        }
    </script>
</body>
</html>
