/* Critical CSS for above-the-fold content */
/* This CSS should be inlined for fastest loading */

/* Reset and base styles */
*,*::before,*::after{box-sizing:border-box}
html{line-height:1.15;-webkit-text-size-adjust:100%}
body{margin:0;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif}

/* Preloader styles */
.preload{
    background-color:#0f1416;
    min-width:320px;
    position:fixed;
    z-index:500;
    top:0;left:0;right:0;bottom:0;
    display:flex;
    justify-content:center;
    align-items:center;
    flex-direction:column;
    box-sizing:border-box;
}

.preload__progress{
    width:200px;
    height:4px;
    background:#333;
    border-radius:2px;
    overflow:hidden;
    margin-bottom:20px;
}

.preload__progress::after{
    content:'';
    display:block;
    width:0;
    height:100%;
    background:linear-gradient(90deg,#ff6b35,#f7931e);
    animation:preload-progress 3s ease-out forwards;
}

@keyframes preload-progress{
    to{width:100%}
}

/* Header critical styles */
.header{
    position:relative;
    z-index:100;
    background:rgba(15,20,22,0.95);
    backdrop-filter:blur(10px);
}

.container{
    max-width:1200px;
    margin:0 auto;
    padding:0 20px;
}

/* Navigation critical styles */
.navigation{
    position:fixed;
    top:0;
    left:0;
    right:0;
    z-index:200;
    background:rgba(15,20,22,0.98);
    backdrop-filter:blur(15px);
    transition:transform 0.3s ease;
}

/* Background image critical */
.bg{
    position:fixed;
    top:0;left:0;
    width:100%;height:100%;
    z-index:-1;
    overflow:hidden;
}

.bg__img{
    width:100%;
    height:100%;
    object-fit:cover;
    object-position:center;
}

/* Section base styles */
.section{
    position:relative;
    min-height:100vh;
    display:flex;
    align-items:center;
    justify-content:center;
    padding:80px 0;
}

/* Typography critical */
h1,h2,h3{
    margin:0;
    font-weight:700;
    line-height:1.2;
    color:#fff;
}

h1{font-size:clamp(2rem,5vw,4rem)}
h2{font-size:clamp(1.5rem,4vw,3rem)}
h3{font-size:clamp(1.25rem,3vw,2rem)}

/* Button critical styles */
.btn{
    display:inline-flex;
    align-items:center;
    justify-content:center;
    padding:12px 24px;
    background:linear-gradient(135deg,#ff6b35,#f7931e);
    color:#fff;
    text-decoration:none;
    border-radius:6px;
    font-weight:600;
    transition:transform 0.2s ease,box-shadow 0.2s ease;
    border:none;
    cursor:pointer;
}

.btn:hover{
    transform:translateY(-2px);
    box-shadow:0 8px 25px rgba(255,107,53,0.3);
}

/* Responsive critical */
@media (max-width:768px){
    .container{padding:0 15px}
    .section{padding:60px 0}
    h1{font-size:2.5rem}
    h2{font-size:2rem}
    h3{font-size:1.5rem}
}

/* Loading animation */
@keyframes fadeIn{
    from{opacity:0;transform:translateY(20px)}
    to{opacity:1;transform:translateY(0)}
}

.fade-in{
    animation:fadeIn 0.6s ease-out forwards;
}

/* Hide non-critical content initially */
.lazy-load{
    opacity:0;
    transform:translateY(30px);
    transition:opacity 0.6s ease,transform 0.6s ease;
}

.lazy-load.loaded{
    opacity:1;
    transform:translateY(0);
}

/* Critical utility classes */
.text-center{text-align:center}
.text-white{color:#fff}
.mb-0{margin-bottom:0}
.mt-0{margin-top:0}
.d-none{display:none}
.d-block{display:block}
.d-flex{display:flex}
.justify-center{justify-content:center}
.align-center{align-items:center}
.w-100{width:100%}
.h-100{height:100%}

/* Performance optimizations */
img{
    max-width:100%;
    height:auto;
    loading:lazy;
}

/* Prevent layout shift */
.aspect-ratio-16-9{
    aspect-ratio:16/9;
    overflow:hidden;
}

.aspect-ratio-1-1{
    aspect-ratio:1/1;
    overflow:hidden;
}
