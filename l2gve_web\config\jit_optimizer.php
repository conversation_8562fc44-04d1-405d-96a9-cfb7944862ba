<?php
declare(strict_types=1);

/**
 * PHP 8.4 JIT Optimizer and Performance Monitor
 * This class provides JIT optimization and performance monitoring capabilities
 */
class JITOptimizer {
    private array $performanceMetrics = [];
    private float $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
    }
    
    /**
     * Initialize JIT optimizations for PHP 8.4
     */
    public function initializeJIT(): array {
        $status = [
            'jit_enabled' => false,
            'jit_buffer_size' => 0,
            'jit_mode' => 'disabled',
            'opcache_enabled' => false,
            'recommendations' => []
        ];
        
        // Check if OPcache is enabled
        if (!extension_loaded('Zend OPcache')) {
            $status['recommendations'][] = 'Install and enable OPcache extension';
            return $status;
        }
        
        $opcacheStatus = opcache_get_status();
        $status['opcache_enabled'] = $opcacheStatus !== false;
        
        if (!$status['opcache_enabled']) {
            $status['recommendations'][] = 'Enable OPcache in php.ini';
            return $status;
        }
        
        // Check JIT configuration
        $jitBufferSize = ini_get('opcache.jit_buffer_size');
        $jitMode = ini_get('opcache.jit');
        
        $status['jit_enabled'] = !empty($jitBufferSize) && $jitBufferSize !== '0';
        $status['jit_buffer_size'] = $this->parseMemorySize($jitBufferSize);
        $status['jit_mode'] = $jitMode;
        
        // Provide recommendations
        if (!$status['jit_enabled']) {
            $status['recommendations'][] = 'Enable JIT by setting opcache.jit_buffer_size=512M';
            $status['recommendations'][] = 'Set opcache.jit=1255 for optimal performance';
        } elseif ($status['jit_buffer_size'] < 256 * 1024 * 1024) {
            $status['recommendations'][] = 'Increase JIT buffer size to at least 256M';
        }
        
        // Check JIT mode
        if ($jitMode !== '1255') {
            $status['recommendations'][] = 'Use JIT mode 1255 for best performance in PHP 8.4';
        }
        
        return $status;
    }
    
    /**
     * Warm up JIT compiler with critical code paths
     */
    public function warmupJIT(): void {
        echo "Warming up JIT compiler...\n";
        
        // Critical code paths to warm up
        $warmupTasks = [
            fn() => $this->warmupStringOperations(),
            fn() => $this->warmupArrayOperations(),
            fn() => $this->warmupMathOperations(),
            fn() => $this->warmupObjectOperations(),
            fn() => $this->warmupDatabaseOperations(),
        ];
        
        foreach ($warmupTasks as $task) {
            $start = microtime(true);
            $task();
            $duration = (microtime(true) - $start) * 1000;
            echo "✓ Warmup task completed in {$duration}ms\n";
        }
    }
    
    private function warmupStringOperations(): void {
        // String operations that benefit from JIT
        for ($i = 0; $i < 1000; $i++) {
            $str = "test string $i";
            $str = strtoupper($str);
            $str = str_replace('TEST', 'test', $str);
            $hash = md5($str);
            $json = json_encode(['data' => $str, 'hash' => $hash]);
            $decoded = json_decode($json, true);
        }
    }
    
    private function warmupArrayOperations(): void {
        // Array operations
        $data = range(1, 1000);
        for ($i = 0; $i < 100; $i++) {
            $filtered = array_filter($data, fn($x) => $x % 2 === 0);
            $mapped = array_map(fn($x) => $x * 2, $filtered);
            $sum = array_sum($mapped);
            $sorted = sort($mapped);
        }
    }
    
    private function warmupMathOperations(): void {
        // Mathematical operations
        for ($i = 0; $i < 10000; $i++) {
            $result = sin($i) + cos($i) + sqrt($i + 1);
            $result = round($result, 4);
        }
    }
    
    private function warmupObjectOperations(): void {
        // Object creation and method calls
        for ($i = 0; $i < 1000; $i++) {
            $obj = new stdClass();
            $obj->id = $i;
            $obj->name = "Object $i";
            $obj->timestamp = time();
            
            $serialized = serialize($obj);
            $unserialized = unserialize($serialized);
        }
    }
    
    private function warmupDatabaseOperations(): void {
        // Simulate database-like operations
        $data = [];
        for ($i = 0; $i < 1000; $i++) {
            $data[] = [
                'id' => $i,
                'name' => "User $i",
                'email' => "user$<EMAIL>",
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        
        // Simulate queries
        $filtered = array_filter($data, fn($row) => $row['id'] > 500);
        $sorted = usort($filtered, fn($a, $b) => $a['name'] <=> $b['name']);
    }
    
    /**
     * Monitor performance metrics
     */
    public function getPerformanceMetrics(): array {
        $currentTime = microtime(true);
        $memoryUsage = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        $metrics = [
            'execution_time' => round(($currentTime - $this->startTime) * 1000, 2),
            'memory_usage' => $this->formatBytes($memoryUsage),
            'peak_memory' => $this->formatBytes($peakMemory),
            'opcache_stats' => $this->getOPcacheStats(),
            'jit_stats' => $this->getJITStats(),
        ];
        
        return $metrics;
    }
    
    private function getOPcacheStats(): array {
        $status = opcache_get_status();
        if (!$status) {
            return ['enabled' => false];
        }
        
        return [
            'enabled' => true,
            'memory_usage' => $this->formatBytes($status['memory_usage']['used_memory']),
            'memory_free' => $this->formatBytes($status['memory_usage']['free_memory']),
            'cached_scripts' => count($status['scripts']),
            'hit_rate' => round($status['opcache_statistics']['opcache_hit_rate'], 2),
            'cache_full' => $status['cache_full'] ?? false,
        ];
    }
    
    private function getJITStats(): array {
        if (!function_exists('opcache_get_status')) {
            return ['available' => false];
        }
        
        $status = opcache_get_status();
        $jitInfo = $status['jit'] ?? null;
        
        if (!$jitInfo) {
            return ['available' => false];
        }
        
        return [
            'available' => true,
            'enabled' => $jitInfo['enabled'] ?? false,
            'kind' => $jitInfo['kind'] ?? 'unknown',
            'opt_level' => $jitInfo['opt_level'] ?? 0,
            'opt_flags' => $jitInfo['opt_flags'] ?? 0,
        ];
    }
    
    /**
     * Generate optimization report
     */
    public function generateOptimizationReport(): string {
        $jitStatus = $this->initializeJIT();
        $metrics = $this->getPerformanceMetrics();
        
        $report = "PHP 8.4 JIT Optimization Report\n";
        $report .= str_repeat('=', 50) . "\n\n";
        
        $report .= "System Information:\n";
        $report .= "- PHP Version: " . PHP_VERSION . "\n";
        $report .= "- OPcache: " . ($jitStatus['opcache_enabled'] ? 'Enabled' : 'Disabled') . "\n";
        $report .= "- JIT: " . ($jitStatus['jit_enabled'] ? 'Enabled' : 'Disabled') . "\n";
        $report .= "- JIT Mode: " . $jitStatus['jit_mode'] . "\n";
        $report .= "- JIT Buffer: " . $this->formatBytes($jitStatus['jit_buffer_size']) . "\n\n";
        
        $report .= "Performance Metrics:\n";
        $report .= "- Execution Time: " . $metrics['execution_time'] . "ms\n";
        $report .= "- Memory Usage: " . $metrics['memory_usage'] . "\n";
        $report .= "- Peak Memory: " . $metrics['peak_memory'] . "\n\n";
        
        if ($metrics['opcache_stats']['enabled']) {
            $report .= "OPcache Statistics:\n";
            $report .= "- Memory Usage: " . $metrics['opcache_stats']['memory_usage'] . "\n";
            $report .= "- Cached Scripts: " . $metrics['opcache_stats']['cached_scripts'] . "\n";
            $report .= "- Hit Rate: " . $metrics['opcache_stats']['hit_rate'] . "%\n\n";
        }
        
        if (!empty($jitStatus['recommendations'])) {
            $report .= "Recommendations:\n";
            foreach ($jitStatus['recommendations'] as $recommendation) {
                $report .= "- " . $recommendation . "\n";
            }
            $report .= "\n";
        }
        
        return $report;
    }
    
    private function parseMemorySize(string $size): int {
        if (empty($size)) return 0;
        
        $size = trim($size);
        $unit = strtoupper(substr($size, -1));
        $value = (int)substr($size, 0, -1);
        
        return match($unit) {
            'G' => $value * 1024 * 1024 * 1024,
            'M' => $value * 1024 * 1024,
            'K' => $value * 1024,
            default => (int)$size
        };
    }
    
    private function formatBytes(int $bytes): string {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor(log($bytes, 1024));
        return round($bytes / (1024 ** $factor), 2) . ' ' . $units[$factor];
    }
}

// Usage example
if (php_sapi_name() === 'cli') {
    $optimizer = new JITOptimizer();
    
    echo $optimizer->generateOptimizationReport();
    
    if (isset($argv[1]) && $argv[1] === '--warmup') {
        $optimizer->warmupJIT();
        echo "\nJIT warmup completed!\n";
    }
}
