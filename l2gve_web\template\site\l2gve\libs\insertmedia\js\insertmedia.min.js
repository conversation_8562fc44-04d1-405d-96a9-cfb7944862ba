function insertmedia(t){let e=function(){let t={},i=!1,r=0,n=arguments.length;for("[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(i=arguments[0],r++);r<n;r++){let h=arguments[r];!function(r){for(let n in r)Object.prototype.hasOwnProperty.call(r,n)&&(i&&"[object Object]"===Object.prototype.toString.call(r[n])?t[n]=e(!0,t[n],r[n]):t[n]=r[n])}(h)}return t},i=e({delay:300,immediately:!0,attr:"data-insertmedia"},t),r=1,n=function(t,i){let r=e({src:"",width:"300",height:"200",setting:""},i);t.innerHTML=`<iframe width="${r.width}" height="${r.height}" src="${r.src}${r.setting}" frameborder="0" allowfullscreen="true" scrolling="no"></iframe>`},h=function(t,i){let r=e({src:"",width:"300",height:"200",setting:""},i);t.innerHTML=`<iframe width="${r.width}" height="${r.height}" src="${r.src}${r.setting}" frameborder="0" allowfullscreen="true" scrolling="no" ></iframe>`},l=function(t,i){let r=e({src:"",width:"300",height:"200",setting:""},i);t.innerHTML=`<iframe width="${r.width}" height="${r.height}" src="${r.src}${r.setting}" frameborder="0" allowfullscreen="true" scrolling="no" ></iframe>`},o=function(t,i){let r=e({src:"",width:"300",height:"200",setting:""},i);t.innerHTML=`<iframe width="${r.width}" height="${r.height}" src="${r.src}${r.setting}" frameborder="0" allowfullscreen="true" scrolling="no" ></iframe>`},c=function(t,i){let r=e({src:"",width:"300",height:"200",setting:""},i);t.innerHTML=`<img width="${r.width}" height="${r.height}" src="${r.src}">`},s=function(t,i){let r=e({src:"",width:"300",height:"200",setting:""},i);t.innerHTML=`<video src="${r.src}" ${r.setting}></video>`};document.querySelectorAll(`[${i.attr}]`).forEach(function(t,e,g){setTimeout(()=>{let e=JSON.parse(`${t.getAttribute(i.attr)}`);(e.type||e.src)&&("youtube"==e.type&&n(t,e),"twitch"==e.type&&h(t,e),"trovo"==e.type&&l(t,e),"frame"==e.type&&o(t,e),"img"==e.type&&c(t,e),"html5"==e.type&&s(t,e))},i.immediately?i.delay:i.delay*r++)})}