<!DOCTYPE html>
<html>
<head>
    <title>News Management</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .news-list { margin-top: 30px; }
        .news-item { border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; }
        .news-actions {
            margin-top: 10px;
        }
        .btn-edit, .btn-delete {
            padding: 5px 15px;
            margin-right: 10px;
            border: none;
            cursor: pointer;
            border-radius: 3px;
        }
        .btn-edit {
            background: #28a745;
            color: white;
        }
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        .btn-edit:hover {
            background: #218838;
        }
        .btn-delete:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <h1><PERSON><PERSON><PERSON><PERSON> lý tin tức</h1>
    
    <form id="newsForm">
        <div class="form-group">
            <label>Tiêu đề:</label>
            <input type="text" id="title" required>
        </div>
        
        <div class="form-group">
            <label>Mô tả ngắn:</label>
            <textarea id="description" rows="3" required></textarea>
        </div>
        
        <div class="form-group">
            <label>Nội dung:</label>
            <textarea id="content" rows="5"></textarea>
        </div>
        
        <div class="form-group">
            <label>Hình ảnh URL:</label>
            <input type="url" id="image">
        </div>
        
        <div class="form-group">
            <label>Loại tin:</label>
            <select id="type">
                <option value="news">Tin tức</option>
                <option value="promotions">Khuyến mãi</option>
                <option value="updates">Cập nhật</option>
                <option value="features">Tính năng</option>
            </select>
        </div>
        
        <button type="submit">Tạo tin tức</button>
    </form>
    
    <div class="news-list" id="newsList"></div>

    <script>
        let editingNewsId = null;

        // Tạo tin tức mới
        document.getElementById('newsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const newsData = {
                title: document.getElementById('title').value,
                description: document.getElementById('description').value,
                content: document.getElementById('content').value,
                image: document.getElementById('image').value,
                type: document.getElementById('type').value
            };
            
            const method = editingNewsId ? 'PUT' : 'POST';
            const url = editingNewsId ? `../api/news.php?id=${editingNewsId}` : '../api/news.php';
            
            fetch(url, {
                method: method,
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(newsData)
            })
            .then(response => response.json())
            .then(data => {
                alert(editingNewsId ? 'Tin tức đã được cập nhật!' : 'Tin tức đã được tạo!');
                loadNews();
                resetForm();
            });
        });

        // Load danh sách tin tức với nút sửa/xóa
        function loadNews() {
            fetch('../api/news.php')
            .then(response => response.json())
            .then(data => {
                const newsList = document.getElementById('newsList');
                newsList.innerHTML = '<h2>Danh sách tin tức</h2>';
                
                data.forEach(news => {
                    newsList.innerHTML += `
                        <div class="news-item">
                            <h3>${news.title}</h3>
                            <p><strong>Loại:</strong> ${news.type}</p>
                            <p><strong>Ngày:</strong> ${news.date}</p>
                            <p>${news.desc}</p>
                            <div class="news-actions">
                                <button onclick="editNews(${news.id})" class="btn-edit">Sửa</button>
                                <button onclick="deleteNews(${news.id})" class="btn-delete">Xóa</button>
                            </div>
                        </div>
                    `;
                });
            });
        }

        // Sửa tin tức
        function editNews(id) {
            fetch(`../api/news.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (!data.error) {
                    editingNewsId = id;
                    document.getElementById('title').value = data.title;
                    document.getElementById('description').value = data.desc;
                    document.getElementById('content').value = data.content || '';
                    document.getElementById('image').value = data.img || '';
                    document.getElementById('type').value = data.type;
                    
                    document.querySelector('button[type="submit"]').textContent = 'Cập nhật tin tức';
                    window.scrollTo(0, 0);
                }
            });
        }

        // Xóa tin tức
        function deleteNews(id) {
            if (confirm('Bạn có chắc chắn muốn xóa tin tức này?')) {
                fetch(`../api/news.php?id=${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    alert('Tin tức đã được xóa!');
                    loadNews();
                });
            }
        }

        // Reset form
        function resetForm() {
            editingNewsId = null;
            document.getElementById('newsForm').reset();
            document.querySelector('button[type="submit"]').textContent = 'Tạo tin tức';
        }

        // Load tin tức khi trang được tải
        loadNews();
    </script>
</body>
</html>

