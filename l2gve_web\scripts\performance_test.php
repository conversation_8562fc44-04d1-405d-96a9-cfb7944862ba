<?php
declare(strict_types=1);

require_once __DIR__ . '/../config/autoload.php';
require_once __DIR__ . '/../config/jit_optimizer.php';
require_once __DIR__ . '/../config/asset_manager.php';
require_once __DIR__ . '/../config/query_builder.php';

/**
 * Comprehensive Performance Test Suite for L2GVE PHP 8.4 Optimization
 */
class PerformanceTestSuite {
    private array $results = [];
    private JITOptimizer $optimizer;
    
    public function __construct() {
        $this->optimizer = new JITOptimizer();
    }
    
    public function runAllTests(): array {
        echo "🚀 Starting L2GVE Performance Test Suite...\n";
        echo str_repeat('=', 60) . "\n";
        
        $tests = [
            'PHP Core Performance' => fn() => $this->testPHPCorePerformance(),
            'Database Operations' => fn() => $this->testDatabasePerformance(),
            'Cache Performance' => fn() => $this->testCachePerformance(),
            'Asset Optimization' => fn() => $this->testAssetOptimization(),
            'Memory Usage' => fn() => $this->testMemoryUsage(),
            'JIT Compilation' => fn() => $this->testJITPerformance(),
            'File I/O Operations' => fn() => $this->testFileIOPerformance(),
            'JSON Processing' => fn() => $this->testJSONPerformance(),
        ];
        
        foreach ($tests as $testName => $testFunction) {
            echo "\n📊 Running: $testName\n";
            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);
            
            try {
                $result = $testFunction();
                $endTime = microtime(true);
                $endMemory = memory_get_usage(true);
                
                $this->results[$testName] = [
                    'status' => 'success',
                    'execution_time' => round(($endTime - $startTime) * 1000, 2),
                    'memory_used' => $endMemory - $startMemory,
                    'result' => $result,
                ];
                
                echo "✅ Completed in {$this->results[$testName]['execution_time']}ms\n";
                
            } catch (Exception $e) {
                $this->results[$testName] = [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                    'execution_time' => round((microtime(true) - $startTime) * 1000, 2),
                ];
                echo "❌ Failed: {$e->getMessage()}\n";
            }
        }
        
        return $this->results;
    }
    
    private function testPHPCorePerformance(): array {
        $iterations = 100000;
        
        // String operations
        $stringStart = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            $str = "test string $i";
            $str = strtoupper($str);
            $str = str_replace('TEST', 'test', $str);
            $hash = md5($str);
        }
        $stringTime = (microtime(true) - $stringStart) * 1000;
        
        // Array operations
        $arrayStart = microtime(true);
        $data = range(1, 1000);
        for ($i = 0; $i < 1000; $i++) {
            $filtered = array_filter($data, fn($x) => $x % 2 === 0);
            $mapped = array_map(fn($x) => $x * 2, $filtered);
            $sum = array_sum($mapped);
        }
        $arrayTime = (microtime(true) - $arrayStart) * 1000;
        
        // Math operations
        $mathStart = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            $result = sin($i) + cos($i) + sqrt($i + 1);
        }
        $mathTime = (microtime(true) - $mathStart) * 1000;
        
        return [
            'string_operations' => round($stringTime, 2) . 'ms',
            'array_operations' => round($arrayTime, 2) . 'ms',
            'math_operations' => round($mathTime, 2) . 'ms',
            'total_operations' => $iterations + 1000 + $iterations,
        ];
    }
    
    private function testDatabasePerformance(): array {
        try {
            $db = Database::getInstance();
            $queryBuilder = new QueryBuilder($db);
            
            // Test connection time
            $connStart = microtime(true);
            $connection = $db->getConnection();
            $connTime = (microtime(true) - $connStart) * 1000;
            
            // Test simple query
            $queryStart = microtime(true);
            $stmt = $connection->prepare("SELECT 1 as test, NOW() as current_time");
            $stmt->execute();
            $result = $stmt->fetch();
            $queryTime = (microtime(true) - $queryStart) * 1000;
            
            // Test query builder
            $builderStart = microtime(true);
            $builder = $queryBuilder->reset();
            $sql = $builder->table('test_table')
                          ->select(['id', 'name', 'created_at'])
                          ->where('status', '=', 'active')
                          ->orderBy('created_at', 'DESC')
                          ->limit(10)
                          ->toSql();
            $builderTime = (microtime(true) - $builderStart) * 1000;
            
            return [
                'connection_time' => round($connTime, 2) . 'ms',
                'simple_query_time' => round($queryTime, 2) . 'ms',
                'query_builder_time' => round($builderTime, 2) . 'ms',
                'query_result' => $result,
                'generated_sql' => $sql,
            ];
            
        } catch (Exception $e) {
            return [
                'error' => 'Database test failed: ' . $e->getMessage(),
                'connection_available' => false,
            ];
        }
    }
    
    private function testCachePerformance(): array {
        $cache = CacheManager::getInstance();
        $iterations = 1000;
        
        // Test cache write performance
        $writeStart = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            $cache->set("test_key_$i", "test_value_$i", 3600);
        }
        $writeTime = (microtime(true) - $writeStart) * 1000;
        
        // Test cache read performance
        $readStart = microtime(true);
        $hits = 0;
        for ($i = 0; $i < $iterations; $i++) {
            $value = $cache->get("test_key_$i");
            if ($value !== null) $hits++;
        }
        $readTime = (microtime(true) - $readStart) * 1000;
        
        // Test batch operations
        $batchData = [];
        for ($i = 0; $i < 100; $i++) {
            $batchData["batch_key_$i"] = "batch_value_$i";
        }
        
        $batchStart = microtime(true);
        $cache->setMultiple($batchData, 3600);
        $batchWriteTime = (microtime(true) - $batchStart) * 1000;
        
        $batchReadStart = microtime(true);
        $batchResults = $cache->getMultiple(array_keys($batchData));
        $batchReadTime = (microtime(true) - $batchReadStart) * 1000;
        
        return [
            'write_time' => round($writeTime, 2) . 'ms',
            'read_time' => round($readTime, 2) . 'ms',
            'hit_rate' => round(($hits / $iterations) * 100, 2) . '%',
            'batch_write_time' => round($batchWriteTime, 2) . 'ms',
            'batch_read_time' => round($batchReadTime, 2) . 'ms',
            'operations_tested' => $iterations * 2 + 200,
        ];
    }
    
    private function testAssetOptimization(): array {
        $assetManager = AssetManager::createDefault();
        
        // Test CSS optimization
        $cssStart = microtime(true);
        $cssContent = $assetManager->renderCSSLinks(false);
        $cssTime = (microtime(true) - $cssStart) * 1000;
        
        // Test JS optimization
        $jsStart = microtime(true);
        $jsContent = $assetManager->renderJSScripts();
        $jsTime = (microtime(true) - $jsStart) * 1000;
        
        // Test preload generation
        $preloadStart = microtime(true);
        $preloadContent = $assetManager->renderPreloadTags();
        $preloadTime = (microtime(true) - $preloadStart) * 1000;
        
        return [
            'css_generation_time' => round($cssTime, 2) . 'ms',
            'js_generation_time' => round($jsTime, 2) . 'ms',
            'preload_generation_time' => round($preloadTime, 2) . 'ms',
            'css_size' => strlen($cssContent) . ' bytes',
            'js_size' => strlen($jsContent) . ' bytes',
            'preload_size' => strlen($preloadContent) . ' bytes',
        ];
    }
    
    private function testMemoryUsage(): array {
        $startMemory = memory_get_usage(true);
        $startPeakMemory = memory_get_peak_usage(true);
        
        // Create memory-intensive operations
        $data = [];
        for ($i = 0; $i < 10000; $i++) {
            $data[] = [
                'id' => $i,
                'name' => "User $i",
                'email' => "user$<EMAIL>",
                'data' => str_repeat('x', 100),
                'timestamp' => time(),
            ];
        }
        
        // Process data
        $processed = array_map(function($item) {
            return [
                'id' => $item['id'],
                'hash' => md5($item['name'] . $item['email']),
                'size' => strlen($item['data']),
            ];
        }, $data);
        
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);
        
        // Clean up
        unset($data, $processed);
        gc_collect_cycles();
        
        $afterCleanupMemory = memory_get_usage(true);
        
        return [
            'start_memory' => $this->formatBytes($startMemory),
            'end_memory' => $this->formatBytes($endMemory),
            'peak_memory' => $this->formatBytes($endPeakMemory),
            'after_cleanup' => $this->formatBytes($afterCleanupMemory),
            'memory_used' => $this->formatBytes($endMemory - $startMemory),
            'memory_freed' => $this->formatBytes($endMemory - $afterCleanupMemory),
            'items_processed' => 10000,
        ];
    }
    
    private function testJITPerformance(): array {
        // Test JIT compilation benefits
        $iterations = 50000;
        
        // CPU-intensive function that benefits from JIT
        $jitStart = microtime(true);
        $result = 0;
        for ($i = 0; $i < $iterations; $i++) {
            $result += $this->complexCalculation($i);
        }
        $jitTime = (microtime(true) - $jitStart) * 1000;
        
        return [
            'jit_test_time' => round($jitTime, 2) . 'ms',
            'iterations' => $iterations,
            'result' => $result,
            'avg_time_per_iteration' => round($jitTime / $iterations, 4) . 'ms',
            'jit_status' => ini_get('opcache.jit') ? 'Enabled' : 'Disabled',
        ];
    }
    
    private function complexCalculation(int $n): float {
        return sin($n) * cos($n) + sqrt($n + 1) * log($n + 1);
    }
    
    private function testFileIOPerformance(): array {
        $testFile = __DIR__ . '/../cache/performance_test.tmp';
        $iterations = 1000;
        
        // Write test
        $writeStart = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            file_put_contents($testFile, "Test data $i\n", FILE_APPEND | LOCK_EX);
        }
        $writeTime = (microtime(true) - $writeStart) * 1000;
        
        // Read test
        $readStart = microtime(true);
        $content = file_get_contents($testFile);
        $lines = explode("\n", $content);
        $readTime = (microtime(true) - $readStart) * 1000;
        
        // Cleanup
        if (file_exists($testFile)) {
            unlink($testFile);
        }
        
        return [
            'write_time' => round($writeTime, 2) . 'ms',
            'read_time' => round($readTime, 2) . 'ms',
            'file_size' => strlen($content) . ' bytes',
            'lines_written' => $iterations,
            'lines_read' => count($lines) - 1,
        ];
    }
    
    private function testJSONPerformance(): array {
        $data = [];
        for ($i = 0; $i < 1000; $i++) {
            $data[] = [
                'id' => $i,
                'name' => "User $i",
                'email' => "user$<EMAIL>",
                'metadata' => [
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'status' => 'active',
                    'tags' => ['user', 'active', "group_$i"],
                ],
            ];
        }
        
        // JSON encode test
        $encodeStart = microtime(true);
        $json = json_encode($data);
        $encodeTime = (microtime(true) - $encodeStart) * 1000;
        
        // JSON decode test
        $decodeStart = microtime(true);
        $decoded = json_decode($json, true);
        $decodeTime = (microtime(true) - $decodeStart) * 1000;
        
        return [
            'encode_time' => round($encodeTime, 2) . 'ms',
            'decode_time' => round($decodeTime, 2) . 'ms',
            'json_size' => strlen($json) . ' bytes',
            'items_processed' => count($data),
            'decoded_items' => count($decoded),
        ];
    }
    
    private function formatBytes(int $bytes): string {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor(log($bytes, 1024));
        return round($bytes / (1024 ** $factor), 2) . ' ' . $units[$factor];
    }
    
    public function generateReport(): string {
        $report = "L2GVE Performance Test Report\n";
        $report .= str_repeat('=', 60) . "\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $report .= "PHP Version: " . PHP_VERSION . "\n\n";
        
        foreach ($this->results as $testName => $result) {
            $report .= "$testName:\n";
            $report .= str_repeat('-', 30) . "\n";
            
            if ($result['status'] === 'success') {
                $report .= "Status: ✅ Success\n";
                $report .= "Execution Time: {$result['execution_time']}ms\n";
                $report .= "Memory Used: " . $this->formatBytes($result['memory_used']) . "\n";
                
                if (is_array($result['result'])) {
                    foreach ($result['result'] as $key => $value) {
                        $report .= ucfirst(str_replace('_', ' ', $key)) . ": $value\n";
                    }
                }
            } else {
                $report .= "Status: ❌ Error\n";
                $report .= "Error: {$result['error']}\n";
            }
            
            $report .= "\n";
        }
        
        return $report;
    }
}

// Run tests if called directly
if (php_sapi_name() === 'cli') {
    $testSuite = new PerformanceTestSuite();
    $results = $testSuite->runAllTests();
    
    echo "\n" . str_repeat('=', 60) . "\n";
    echo "📋 PERFORMANCE TEST SUMMARY\n";
    echo str_repeat('=', 60) . "\n";
    
    $successCount = 0;
    $totalTime = 0;
    
    foreach ($results as $testName => $result) {
        if ($result['status'] === 'success') {
            $successCount++;
            $totalTime += $result['execution_time'];
            echo "✅ $testName: {$result['execution_time']}ms\n";
        } else {
            echo "❌ $testName: FAILED\n";
        }
    }
    
    echo str_repeat('-', 60) . "\n";
    echo "Total Tests: " . count($results) . "\n";
    echo "Successful: $successCount\n";
    echo "Failed: " . (count($results) - $successCount) . "\n";
    echo "Total Execution Time: {$totalTime}ms\n";
    
    // Save detailed report
    $reportFile = __DIR__ . '/../cache/performance_report_' . date('Y-m-d_H-i-s') . '.txt';
    file_put_contents($reportFile, $testSuite->generateReport());
    echo "Detailed report saved to: $reportFile\n";
}
