<?php
declare(strict_types=1);

$allowedHosts = ['127.0.0.1', '::1', 'localhost'];
$allowedDomains = ['l2gve.local', 'localhost'];

$isAllowed = in_array($_SERVER['REMOTE_ADDR'], $allowedHosts) || 
             in_array($_SERVER['HTTP_HOST'], $allowedDomains);

if (!$isAllowed) {
    die('Access denied');
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>L2GVE Admin Panel</title>
    <style>
        .admin-panel { padding: 20px; font-family: Arial, sans-serif; }
        .btn { padding: 10px 20px; margin: 10px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; display: inline-block; }
        .btn:hover { background: #005a87; }
        .section { margin: 20px 0; }
    </style>
</head>
<body>
    <div class="admin-panel">
        <h1>🎮 L2GVE Admin Panel</h1>
        
        <div class="section">
            <h3>📦 Cache Management</h3>
            <?php if (!is_dir('../cache/')): ?>
                <a href="setup_cache.php" class="btn" style="background: #ff6b35;">⚙️ Setup Cache</a>
            <?php endif; ?>
            <a href="warm_cache_web.php" class="btn">🔥 Warm Cache</a>
            <a href="clear_cache.php" class="btn">🗑️ Clear Cache</a>
        </div>
        
        <div class="section">
            <h3>🌐 Quick Links</h3>
            <a href="/en/" class="btn">🇺🇸 English Site</a>
            <a href="/vn/" class="btn">🇻🇳 Vietnamese Site</a>
        </div>
        
        <div class="section">
            <h3>📊 Status</h3>
            <p>Domain: <strong><?= $_SERVER['HTTP_HOST'] ?></strong></p>
            <p>Cache Directory: <strong><?= is_dir('../cache/') ? '✅ Ready' : '❌ Missing' ?></strong></p>
        </div>
    </div>
</body>
</html>


