<?php
declare(strict_types=1);

require_once 'cache.php';

enum PageType: string {
    case WIKI = 'wiki';
    case INDEX = 'index';
    case NEWS = 'news';
    case DOWNLOAD = 'download';
    case REGISTER = 'register';
    
    public function getTemplate(): string {
        return match($this) {
            self::WIKI => 'wiki.html',
            self::INDEX => 'index.html',
            self::NEWS => 'news.html',
            self::DOWNLOAD => 'download.html',
            self::REGISTER => 'register.html'
        };
    }
}

readonly class PageRequest {
    public function __construct(
        public PageType $type,
        public string $language = 'en',
        public array $params = []
    ) {}
}

class PageController {
    public function __construct(
        private readonly CacheInterface $cache = CacheManager::getInstance()
    ) {}
    
    public function render(PageRequest $request): string {
        $cacheKey = "page_{$request->language}_{$request->type->value}_" . md5(serialize($request->params));
        
        // Try cache first
        $cached = $this->cache->get($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        // Generate content
        $content = $this->generateContent($request);
        
        // Cache for 1 hour
        $this->cache->set($cacheKey, $content, 3600);
        
        return $content;
    }
    
    private function generateContent(PageRequest $request): string {
        $templatePath = __DIR__ . "/../{$request->language}/{$request->type->getTemplate()}";
        
        if (!file_exists($templatePath)) {
            throw new RuntimeException("Template not found: {$templatePath}");
        }
        
        $content = file_get_contents($templatePath);
        
        // Process template variables
        $content = $this->processTemplate($content, $request);
        
        // Minify HTML
        return $this->minifyHtml($content);
    }
    
    private function processTemplate(string $content, PageRequest $request): string {
        // Replace dynamic content
        $replacements = [
            '{{LANGUAGE}}' => $request->language,
            '{{CACHE_VERSION}}' => time(),
            '{{BASE_URL}}' => $this->getBaseUrl()
        ];
        
        return str_replace(array_keys($replacements), array_values($replacements), $content);
    }
    
    private function minifyHtml(string $html): string {
        // Remove comments
        $html = preg_replace('/<!--(?!<!)[^\[>].*?-->/s', '', $html);
        
        // Remove extra whitespace
        $html = preg_replace('/\s+/', ' ', $html);
        $html = preg_replace('/>\s+</', '><', $html);
        
        return trim($html);
    }
    
    private function getBaseUrl(): string {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        return $protocol . '://' . $_SERVER['HTTP_HOST'];
    }
}
