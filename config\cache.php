<?php
declare(strict_types=1);

enum CacheType: string {
    case FILE = 'file';
    case MEMORY = 'memory';
    case REDIS = 'redis';
}

readonly class CacheConfig {
    public function __construct(
        public CacheType $type = CacheType::FILE,
        public string $path = __DIR__ . '/../cache/',
        public int $ttl = 3600,
        public string $prefix = 'l2gve_'
    ) {}
}

interface CacheInterface {
    public function get(string $key): mixed;
    public function set(string $key, mixed $value, ?int $ttl = null): bool;
    public function delete(string $key): bool;
    public function clear(): bool;
}

class FileCache implements CacheInterface {
    public function __construct(
        private readonly CacheConfig $config = new CacheConfig()
    ) {
        if (!is_dir($this->config->path)) {
            mkdir($this->config->path, 0755, true);
        }
    }
    
    public function get(string $key): mixed {
        $file = $this->getFilePath($key);
        if (!file_exists($file)) return null;
        
        $data = unserialize(file_get_contents($file));
        if ($data['expires'] < time()) {
            unlink($file);
            return null;
        }
        
        return $data['value'];
    }
    
    public function set(string $key, mixed $value, ?int $ttl = null): bool {
        $ttl ??= $this->config->ttl;
        $data = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        
        return file_put_contents($this->getFilePath($key), serialize($data)) !== false;
    }
    
    public function delete(string $key): bool {
        $file = $this->getFilePath($key);
        return file_exists($file) ? unlink($file) : true;
    }
    
    public function clear(): bool {
        $files = glob($this->config->path . $this->config->prefix . '*');
        foreach ($files as $file) {
            unlink($file);
        }
        return true;
    }
    
    private function getFilePath(string $key): string {
        return $this->config->path . $this->config->prefix . md5($key) . '.cache';
    }
}

class CacheManager {
    private static ?CacheInterface $instance = null;
    
    public static function getInstance(): CacheInterface {
        return self::$instance ??= new FileCache();
    }
}