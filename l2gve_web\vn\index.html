<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Load bypass script FIRST -->
    <script src="../bypass-protection.js"></script>
    
    <!-- Then other scripts -->
    <meta charset="UTF-8">
    <!-- Rest of head content -->
    <script src="https://www.googletagmanager.com/gtag/js?id=UA-167385217-1" async="true" ></script><script>window.dataLayer = window.dataLayer || []; 
            function gtag(){dataLayer.push(arguments);} 
            gtag('js', new Date()); gtag('config', 'UA-167385217-1',{ 'anonymize_ip': true });</script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-T99S9NM7');</script><script>   (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
               m[i].l=1*new Date();k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
               (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");
               ym(97762192, "init", {
                    clickmap:true,
                    trackLinks:true,
                    accurateTrackBounce:true,
                    webvisor:true
               });</script><noscript><div><img src="https://mc.yandex.ru/watch/97762192" style="position:absolute; left:-9999px;" alt="" /></div></noscript>        <title>L2GVE - Máy chủ Essence GvE x100</title>
    <meta name="Description" content="Dive into the world of Lineage II with the L2GVE Essence GvE x100 server">
    <meta name="Keywords" content="Essence GvE x100">
       
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="theme-color" content="#0f1416">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="viewport" content="width=device-width">
    <!-- <meta name="author" content="Front-End by Vitalii P. > Get-Web.site"> -->

    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico@v=1" type="image/x-icon">
<link rel="icon" sizes="16x16" href="../template/site/l2gve/images/favicon/favicon-16x16.png@v=1" type="image/png">
<link rel="icon" sizes="32x32" href="../template/site/l2gve/images/favicon/favicon-32x32.png@v=1" type="image/png">
<link rel="apple-touch-icon-precomposed" href="../template/site/l2gve/images/favicon/apple-touch-icon-precomposed.png@v=1">
<link rel="apple-touch-icon" href="../template/site/l2gve/images/favicon/apple-touch-icon.png@v=1">
<link rel="apple-touch-icon" sizes="57x57" href="../template/site/l2gve/images/favicon/apple-touch-icon-57x57.png@v=1">
<link rel="apple-touch-icon" sizes="60x60" href="../template/site/l2gve/images/favicon/apple-touch-icon-60x60.png@v=1">
<link rel="apple-touch-icon" sizes="72x72" href="../template/site/l2gve/images/favicon/apple-touch-icon-72x72.png@v=1">
<link rel="apple-touch-icon" sizes="76x76" href="../template/site/l2gve/images/favicon/apple-touch-icon-76x76.png@v=1">
<link rel="apple-touch-icon" sizes="114x114" href="../template/site/l2gve/images/favicon/apple-touch-icon-114x114.png@v=1">
<link rel="apple-touch-icon" sizes="120x120" href="../template/site/l2gve/images/favicon/apple-touch-icon-120x120.png@v=1">
<link rel="apple-touch-icon" sizes="144x144" href="../template/site/l2gve/images/favicon/apple-touch-icon-144x144.png@v=1">
<link rel="apple-touch-icon" sizes="152x152" href="../template/site/l2gve/images/favicon/apple-touch-icon-152x152.png@v=1">
<link rel="apple-touch-icon" sizes="167x167" href="../template/site/l2gve/images/favicon/apple-touch-icon-167x167.png@v=1">
<link rel="apple-touch-icon" sizes="180x180" href="../template/site/l2gve/images/favicon/apple-touch-icon-180x180.png@v=1">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://l2gve.com/">
    <meta property="og:title" content="L2GVE - Máy chủ Essence GvE x100">
    <meta property="og:image" content="/template/site/l2gve/images/sclbnr-en.jpg">
    <meta property="og:description" content="Dive into the world of Lineage II with the L2GVE Essence GvE x100 server">
    <meta property="og:site_name" content="L2GVE Essence GvE x100">
    <meta property="og:locale" content="en_US">
    <!-- Next tags are optional but recommended -->
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <style>
    .preload {
        background-color: #0f1416;
        min-width: 320px;
        position: fixed;
        z-index: 500;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        box-sizing: border-box;
    }
</style>

<!-- fonts -->
<link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
<link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />

<!-- libs -->
<link rel="stylesheet" href="../template/site/l2gve/libs/gwi/css/gwi.css" />
<link rel="stylesheet" href="../template/site/l2gve/libs/fancybox/css/jquery.fancybox.min.css" />
<link rel="stylesheet" href="../template/site/l2gve/libs/swiper/css/swiper.min.css" />

<!-- Main style -->
<link rel="stylesheet" href="../template/site/l2gve/css/main.css@v=1719174774.css">
<link rel="stylesheet" href="../template/site/l2gve/css/custom.css@v=1714752167.css">
        <script>
        const __config = {
            gFonts: {
                fonts: ["Open Sans:400,500,600,700:latin,vietnamese"],
                delay: 500,
            },
            preload: {
                /* Minimum display time in seconds */
                /* Минимальное время показа в секундах */
                minTime: 3,

                /* Maximum display time in seconds */
                /* Максимальное время показа в секундах */
                maxTime: 10,

                /* Use the load event.
                If the event occurs earlier than min Time, the preloader will be hidden after the expiration of minTime.
                Otherwise, the preloader will be hidden if the event occurs later than minTime, but before maxTime */
                /* Использовать событие load.
                Если событие наступит раньше чем minTime, то прелоадер скроется по истечению minTime.
                Иначе прелоадер скроется если событие наступит позже minTime, но раньше maxTime */
                withOnload: true,

                /* Condition check update rate in seconds */
                /* Скорость обновления проверки условий в секундах */
                timeInterval: 0.5,
            },
            sectionSwitcher: {
                // Включить/отключить переключение секций колесиком
                init: true,
                // Скорость переключения между секциями
                // Switching speed between sections
                speed: 0.4,
                easeType: "power3.out",
            },
            sliders: {
                news: {
                    init: true,
                    loop: false,
                    autoplay: false,
                    autoplayDelay: 10000,
                },
            },
            gwtraslate: {
                /* Original language */
                lang: "vn",

                /* The language we translate into on the first visit*/
                /* Язык, на который переводим при первом посещении */
                /* langFirstVisit: 'en', */

                /* Если скрипт не работает или работает неправильно, раскомментируйте и укажите основной домен в свойстве domain */
                /* If the script does not work or does not work correctly, uncomment and specify the main domain in the domain property */
                /* domain: "Get-Web.Site" */
            },
            scl: [
            /* {
                    type: "c-telegram",
                    link: "#",
                    target: "_blank",
                } */
            ],
            eDate: {
                initial: true,
                /* Формат YODHMS: лет, месяцев, дней, часов, минут, секунд */
                format: "DHMS",
                year: 2025,
                month: 9,
                day: 01,
                hour: 12,
                minute: 00,
                second: 0,
                timeZone: +7,
                endTimeMSG: "",
            },
        };
    </script>
</head>

<body class="body body_home">

    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T99S9NM7" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- <div class="preload">
    <div class="preload__progress" data-preload-progress></div>
    <img src="../template/site/l2gve/images/preload/preload.png" alt="loading.." class="preload__logo" style="width: 9rem" />
    <div class="preload__items">
        <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" />
        <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" />
        <img src="../template/site/l2gve/images/preload/item.png" alt="★" style="height: 1.4rem" />
    </div>
    </div> -->
    <div class="section compensate-for-scrollbar" data-section="navigation">
    <div class="container" data-container="navigation">
        <div class="navigation">
            <div class="navigation__box navigation__box_side_left">
                <a href="/en" class="logo">
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img" style="width: 9rem" />
                    <img src="../template/site/l2gve/images/logo/logo.png" alt="logo" class="logo__img logo__img_hover" />
                </a>

            </div>

           <div class="navigation__menu menu" data-menu>
    <div class="menu__content">
        <ul class="menu__list">
            <li class="menu__el">
                <a href="/en/" class="menu__item" data-menu-close>
                    Home 
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="javascript:;" data-open-window="wow" class="menu__item" data-menu-close>
                    Downloads 
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="/en/wiki" class="menu__item" data-menu-close>
                    Features 
                </a>
            </li>
            <li class="menu__dot"></li>
            <li class="menu__el">
                <a href="/en/#discord" class="menu__item" data-menu-close>
                    Discord 
                </a>
            </li>
            <li class="menu__el menu__el_continer menu__el_desktop_none" data-place-from="scl">
                <div class="scl" data-place-container="scl">
                    <div class="scl__list">
                        <a href="index.html#Telegram" target="_blank" class="scl__item">
                            <i class="gwi gwi_c-telegram scl__ico-c-telegram"></i>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="scl__item">
                            <i class="gwi gwi_discord scl__ico-discord"></i>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>            <div class="navigation__box navigation__box_side_right" data-place-to="auth">
                <div class="navigation__lang lang notranslate">
    <div class="lang__current">
        <div class="lang__name" data-current-lang>en</div>
    </div>
    <div class="lang__list">
        <a href="/vn/" class="lang__link lang__link_sub" data-google-lang="vi">
            <div class="lang__name">vn</div>
        </a>
        <a href="/en/" class="lang__link lang__link_sub" data-google-lang="en">
            <div class="lang__name">en</div>
        </a>
    </div>
</div>


                
            </div>

            <div class="navigation__gw-burger gw-burger" data-gw-burger>
                <div class="gw-burger__box">
                    <div class="gw-burger__line gw-burger__line_pos_top"></div>
                    <div class="gw-burger__line gw-burger__line_pos_middle"></div>
                    <div class="gw-burger__line gw-burger__line_pos_bottom"></div>
                </div>
            </div>
        </div>
        <!-- END  navigation -->
    </div>
</div>
<!-- END  section -->

    <div class="page">

                    <div class="bg">
                <img src="../template/site/l2gve/images/bg/bg-full.jpg" alt="bg" class="bg__img" aria-hidden="true" />
            </div>
            <div class="countdown-main" data-place-to="countdown"></div>
            <header class="section" data-section="header" data-target-section="header" data-target-section-offset="0" id="header">
    <div class="header-scl-box" data-place-to="scl" data-gw-anime="fadeInRight" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s"></div>
    <div class="container" data-container="header">
        <div class="header">
            <div class="header__box">
                <div class="header__countdown" data-place-from="countdown">
                    <div
	class="countdown countdown_pos"
	data-countdown
	data-place-container="countdown"
>
	<div class="countdown__heading">Time until OBT starts:</div>
	<div class="countdown__box">
		<div class="countdown__decor">
			<div class="countdown__decor-top"></div>
		</div>
		<div class="countdown__counter counter notranslate" data-counter></div>
	</div>
</div>
				</div>
                <div class="header__content" data-gw-anime="fadeInUp" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <div class="header__title">Essence GvE 100 - August 16!</div>
                    <div class="header__desc">
                        The Essence GvE server complex presents a new project with x100 rates and a unique concept in the world of Lineage 2                    </div>
                </div>
                <div class="header__btns" data-gw-anime="fadeInDown" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <a href="javascript:;" data-open-window="wow" class="button">Download</a>
                </div>
            </div>
        </div>
        <!-- END  header -->
    </div>
    <!-- END  container -->
</header>
<!-- END  section -->          



                
    
    
    

    
    
    
                

                
    
    
    


<section class="section" data-section="news" data-target-section="news" data-target-section-offset="0" id="news">
    <div class="container" data-container="news">
        <div class="news" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="rating__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">Events</h2>
                <div class="heading__desc">Server information</div>
            </div>
            <div class="news__box" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <div class="news__control">
                    <div class="news__btns">
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="all">
                            All                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="news">
                            News                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="promotions">
                            Promotions                        </a>
                        <a class="btn btn_size_small btn_accent_no" data-sn-btn="features">
                            Features                        </a>
                    </div>
                    <div class="news__arrows arrows">
                        <div class="news__arrow news__arrow_prev arrow arrow_prev" data-slider-prev="news"></div>
                        <div class="news__arrow news__arrow_next arrow arrow_next" data-slider-next="news"></div>
                    </div>
                </div>

                <div class="news__wrap">
                    <div class="news__list" data-slider="news">
                        <div class="swiper-wrapper" data-news-list></div>
                    </div>
                </div>
            </div>
            <!-- <div class="news__dots dots" data-slider-dots="news"></div> -->
        </div>
        <!-- END  news -->
    </div>
    <!-- END  container -->
</section>
<!-- END  section -->


<script>
// Thay thế __config.posts cũ bằng việc load từ API
fetch('/api/news.php')
.then(response => response.json())
.then(data => {
    __config.posts = data;
    // Trigger news display update
    if (typeof updateNewsDisplay === 'function') {
        updateNewsDisplay();
    }
})
.catch(error => {
    console.error('Error loading news:', error);
    // Fallback data nếu API lỗi
    __config.posts = [];
});
</script>

            <section class="section" data-section="rating" data-target-section="rating" data-target-section-offset="0" id="rating">
            <div class="container" data-container="rating">
        <!-- <div class="rating" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="rating__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">Rating</h2>
                <div class="heading__desc">Server information</div>
            </div>
            <div class="rating__box" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
             
                <div class="rating__btns">
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-pvp" data-open-tab-group="servers" data-open-tab-active="true">
                        Top PVP                    </a>
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-pk" data-open-tab-group="servers">
                        Top PK                    </a>
                    
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-clan" data-open-tab-group="servers">
                        Clans                    </a>
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-exp" data-open-tab-group="servers">
                        Top EXP                    </a>
                    <a class="btn btn_size_small btn_accent_no" data-open-tab="server-0-castle" data-open-tab-group="servers">
                        Castles                    </a>
                </div>
                <div class="rating__tabs">
                                                                            </div>
                <div class="rating__links">
                    <a href="rating.html" class="btn btn_size_large"> Full rating </a>
                </div>
            </div>
        </div> -->
        <!-- END  rating -->
    </div>
    <!-- END  container -->
</section>
<!-- END  section -->            <section class="section" data-section="community" data-target-section="community" data-target-section-offset="0" id="community">
    <div class="container" data-container="community">
        <div class="community" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
            <div class="navigation-height-compensate"></div>
            <div class="community__heading heading" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                <h2 class="heading__title">Community</h2>
                <div class="heading__desc">Server information</div>
            </div>
            <div class="community__box" style="justify-content: center; display: flex;">
                <div class="community__container" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                    <div class="community__title">Our social networks</div>
                    <div class="community__social-list">
                        <a href="index.html#Telegram" target="_blank" class="social decbox" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                            <div class="social__pic">
                                <i class="gwi gwi_c-telegram social__c-telegram"></i>
                            </div>
                            <div class="social__title">Telegram</div>
                            <div class="social__desc">Contact us if you have any questions</div>
                        </a>
                        <a href="index.html#Discord" target="_blank" class="social decbox" data-gw-anime="fadeIn" data-gw-anime-delay=".3s" data-gw-anime-duration=".8s">
                            <div class="social__pic">
                                <i class="gwi gwi_discord social__discord"></i>
                            </div>
                            <div class="social__title">discord</div>
                            <div class="social__desc">Contact us if you have any questions</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>            
<!-- END  section -->        
       

    </div> <!-- END page -->

        <ul class="points" data-points>
    <div class="points__overlay"></div>
    <li class="point point_active" data-point-name="header">
        <div class="point__dot"></div>
        <div class="point__name">Home</div>
    </li>
    <li class="point" data-point-name="inform">
        <div class="point__dot"></div>
        <div class="point__name">Features</div>
    </li>
    <li class="point" data-point-name="news">
        <div class="point__dot"></div>
        <div class="point__name">News</div>
    </li>
    <li class="point" data-point-name="community">
        <div class="point__dot"></div>
        <div class="point__name">Community</div>
    </li>
</ul>    
    

















































<!-- downloads START -->
<div style="display: none">
    <div class="ww ww_animated" id="wow">
        <div class="ww__inner">
            <div class="ww__body downloads">
                <div class="ww__close" data-fancybox-close></div>
                <div class="ww__heading heading heading_short">
                    <h2 class="heading__title">Start playing</h2>
                    <div class="heading__desc">Game files</div>
                </div>
                <div class="downloads__list">
                    <div class="downloads__box">
                        <div class="downloads__title">GAME CLIENT</div>
                        <div class="downloads__btns">
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent">GOOGLE DISK</a>
                            <!-- <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">FEX.NET</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Mega</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Torrent</a> -->
                        </div>
                    </div>
                        <div class="downloads__box">
                            <div class="downloads__title">UPDATER</div>
                            <div class="downloads__btns">
                                <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent">GOOGLE DISK</a>
                                <!-- <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">FEX.NET</a>
                                <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Mega</a>
                                <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Torrent</a> -->
                            </div>
                        </div>
                    <div class="downloads__box">
                        <div class="downloads__title">SYSTEM PATCH</div>
                        <div class="downloads__btns">
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent">GOOGLE DISK</a>
                            <!-- <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">FEX.NET</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Mega</a>
                            <a href="index.html#"  target="_blank"  class="btn btn_size_small btn_accent_no">Torrent</a> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- downloads END -->
    <!-- <div class="streams" data-streams="">
	<div class="streams__list">

                                                                                                        
                                    <div class="streams__item streams__item_play" data-insertstream='{ "type" : "youtube", "src" : "https://www.youtube.com/embed/lQJdoepfqb8?origin=l2gve.com&autoplay=0&mute=1" }'></div> <!-- END stream -->
                                                                                                                        
                                    <a
                        href="https://www.youtube.com/embed/SbQE2KFwEUo?origin=l2gve.com&autoplay=0&mute=1"
                        class="streams__item streams__item_play"
                        style="background-image: url(../template/site/l2gve/images/thumb.jpg)"
                        data-fancybox
                    ></a>
                                
        </div>
        <div class="streams__btn" data-streams-open>
            <div class="streams__btn-txt">Streams</div>
        </div>
        <div class="streams__ico" data-streams-open></div>
    </div>
<!-- END streams -->
<div data-streams-overlay data-streams-open></div>
    <!-- Get-Web Libs  -->

<script src="../template/site/l2gve/libs/js-cookie/js/js.cookie.min.js"></script>
<script src="../template/site/l2gve/libs/jquery/jquery-3.4.1.min.js"></script>
<script src="../template/site/l2gve/libs/fancybox/js/jquery.fancybox.js"></script>
<script src="../template/site/l2gve/libs/swiper/js/swiper.min.js"></script>
<script src="../template/site/l2gve/libs/gsap/js/gsap.min.js"></script>
<script src="../template/site/l2gve/libs/gsap/js/ScrollTrigger.min.js"></script>
<script src="../template/site/l2gve/libs/gsap/js/ScrollToPlugin.min.js"></script>
<script src="../template/site/l2gve/libs/google-translate/js/google-translate.js"></script>
<script src="../template/site/l2gve/libs/insertmedia/js/insertmedia.min.js"></script>
<script src="../template/site/l2gve/libs/countdown/js/jquery.plugin.min.js"></script>
<script src="../template/site/l2gve/libs/countdown/js/jquery.countdown.min.js"></script>
<script src="https://l2gve.com/template/site/l2gve/libs/countdown/js/jquery.countdown-en.js"></script>


<!-- Main app -->
<script src="../template/site/l2gve/js/app.js@v=1715210938"></script>
<script src="../template/site/l2gve/js/custom.js@v=1714752167"></script>

<script>
    // Enhanced Back to Top functionality with scroll fix
    document.addEventListener('DOMContentLoaded', function() {
        const backToTopBtn = document.getElementById('backToTop');
        
        // Fix any scroll blocking elements
        document.body.style.overflowX = 'hidden';
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Fix community section scroll issues
        const communityBox = document.querySelector('.community__box');
        if (communityBox) {
            communityBox.style.pointerEvents = 'none';
            
            // Allow pointer events on child elements
            const childElements = communityBox.querySelectorAll('*');
            childElements.forEach(el => {
                if (el.tagName === 'A' || el.classList.contains('social')) {
                    el.style.pointerEvents = 'auto';
                }
            });
        }
        
        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });
        
        // Smooth scroll to top when clicked
        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Smooth scroll with custom duration
            const duration = 1000; // 1 second
            const start = window.pageYOffset;
            const startTime = performance.now();
            
            const animateScroll = (currentTime) => {
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);
                
                // Easing function for smooth animation
                const easeInOutQuad = progress => progress < 0.5 
                    ? 2 * progress * progress 
                    : 1 - Math.pow(-2 * progress + 2, 2) / 2;
                
                window.scrollTo(0, start * (1 - easeInOutQuad(progress)));
                
                if (progress < 1) {
                    requestAnimationFrame(animateScroll);
                }
            };
            
            requestAnimationFrame(animateScroll);
        });
    });
    </script>
    
</body>

</html>

<!-- 
Copyright © l2gve.com
Design: Unsimple 
Front-End Developer: Front-end developer: Vitalii P. |  Get-Web.Site
 -->
