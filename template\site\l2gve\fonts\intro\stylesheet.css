/* Thin */

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Thin.woff2") format("woff2"),
		url("fonts/Intro-Thin.woff") format("woff");
	font-weight: 100;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Thin-Italic.woff2") format("woff2"),
		url("fonts/Intro-Thin-Italic.woff") format("woff");
	font-weight: 100;
	font-style: italic;
	font-display: swap;
}

/* Light */

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Light.woff2") format("woff2"),
		url("fonts/Intro-Light.woff") format("woff");
	font-weight: 300;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Light-Italic.woff2") format("woff2"),
		url("fonts/Intro-Light-Italic.woff") format("woff");
	font-weight: 300;
	font-style: italic;
	font-display: swap;
}

/* Regular */

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Regular.woff2") format("woff2"),
		url("fonts/Intro-Regular.woff") format("woff");
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Regular-Italic.woff2") format("woff2"),
		url("fonts/Intro-Regular-Italic.woff") format("woff");
	font-weight: normal;
	font-style: italic;
	font-display: swap;
}

/* Bold */

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Bold.woff2") format("woff2"),
		url("fonts/Intro-Bold.woff") format("woff");
	font-weight: bold;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Bold-Italic.woff2") format("woff2"),
		url("fonts/Intro-Bold-Italic.woff") format("woff");
	font-weight: bold;
	font-style: italic;
	font-display: swap;
}

/* Black */

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Black.woff2") format("woff2"),
		url("fonts/Intro-Black.woff") format("woff");
	font-weight: 900;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Intro";
	src: url("fonts/Intro-Black-Italic.woff2") format("woff2"),
		url("fonts/Intro-Black-Italic.woff") format("woff");
	font-weight: 900;
	font-style: italic;
	font-display: swap;
}

/* Book */

@font-face {
	font-family: "Intro Book";
	src: url("fonts/Intro-Book.woff2") format("woff2"),
		url("fonts/Intro-Book.woff") format("woff");
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: "Intro Book";
	src: url("fonts/Intro-Book-Italic.woff2") format("woff2"),
		url("fonts/Intro-Book-Italic.woff") format("woff");
	font-weight: normal;
	font-style: italic;
	font-display: swap;
}
