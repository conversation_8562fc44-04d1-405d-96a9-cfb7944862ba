<?php
declare(strict_types=1);

// Security check - cho phép localhost và l2gve.local
$allowedHosts = ['127.0.0.1', '::1', 'localhost'];
$allowedDomains = ['l2gve.local', 'localhost'];

$isAllowed = in_array($_SERVER['REMOTE_ADDR'], $allowedHosts) || 
             in_array($_SERVER['HTTP_HOST'], $allowedDomains);

if (!$isAllowed) {
    die('Access denied');
}

echo "<h2>Cache Warming Tool</h2>";
echo "<pre>";

require_once '../config/autoload.php';
require_once '../config/page_controller.php';

$controller = new PageController();
$languages = ['en', 'vn'];
$pages = [PageType::WIKI, PageType::INDEX];

echo "Starting cache warming...\n\n";

$startTime = microtime(true);
$successCount = 0;
$failCount = 0;

foreach ($languages as $lang) {
    foreach ($pages as $page) {
        try {
            $pageStart = microtime(true);
            $request = new PageRequest($page, $lang);
            $controller->render($request);
            $pageTime = round((microtime(true) - $pageStart) * 1000, 2);
            
            echo "✓ Cached: {$lang}/{$page->value} ({$pageTime}ms)\n";
            $successCount++;
        } catch (Exception $e) {
            echo "✗ Failed: {$lang}/{$page->value} - {$e->getMessage()}\n";
            $failCount++;
        }
    }
}

$totalTime = round((microtime(true) - $startTime) * 1000, 2);

echo "\n" . str_repeat("=", 50) . "\n";
echo "Cache warming completed!\n";
echo "Success: {$successCount} pages\n";
echo "Failed: {$failCount} pages\n";
echo "Total time: {$totalTime}ms\n";
echo "</pre>";

echo "<p><a href='/'>← Back to site</a></p>";

