<?php
declare(strict_types=1);

// PHP 8.4 JIT optimizations
ini_set('opcache.enable', '1');
ini_set('opcache.jit_buffer_size', '256M');
ini_set('opcache.jit', '1255'); // PHP 8.4 improved JIT
ini_set('opcache.memory_consumption', '256');
ini_set('opcache.max_accelerated_files', '20000');
ini_set('opcache.revalidate_freq', '0');
ini_set('opcache.validate_timestamps', '0');

// PHP 8.4 performance settings
ini_set('realpath_cache_size', '4096K');
ini_set('realpath_cache_ttl', '600');

// Error handling
error_reporting(E_ALL);
ini_set('display_errors', '0');
ini_set('log_errors', '1');

// Security
ini_set('expose_php', '0');
ini_set('session.cookie_httponly', '1');
ini_set('session.cookie_secure', '1');
ini_set('session.use_strict_mode', '1');