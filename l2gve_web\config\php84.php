<?php
declare(strict_types=1);

// PHP 8.4 Advanced JIT optimizations
ini_set('opcache.enable', '1');
ini_set('opcache.jit_buffer_size', '512M'); // Increased for better performance
ini_set('opcache.jit', '1255'); // PHP 8.4 improved JIT with function-level optimization
ini_set('opcache.memory_consumption', '512'); // Increased memory
ini_set('opcache.max_accelerated_files', '50000'); // More files for large projects
ini_set('opcache.revalidate_freq', '0'); // No revalidation in production
ini_set('opcache.validate_timestamps', '0'); // Disable for production
ini_set('opcache.save_comments', '0'); // Save memory
ini_set('opcache.enable_file_override', '1'); // PHP 8.4 file override optimization
ini_set('opcache.optimization_level', '0x7FFEBFFF'); // Maximum optimization
ini_set('opcache.preload_user', 'www-data'); // Set preload user

// PHP 8.4 Enhanced performance settings
ini_set('realpath_cache_size', '8192K'); // Doubled cache size
ini_set('realpath_cache_ttl', '1200'); // Longer TTL for better caching
ini_set('max_execution_time', '60'); // Reasonable limit
ini_set('memory_limit', '512M'); // Adequate memory for complex operations

// PHP 8.4 New features optimization
ini_set('zend.exception_ignore_args', '1'); // Reduce memory usage in exceptions
ini_set('zend.exception_string_param_max_len', '15'); // Limit exception string length

// Error handling
error_reporting(E_ALL);
ini_set('display_errors', '0');
ini_set('log_errors', '1');
ini_set('log_errors_max_len', '1024');

// Security enhancements
ini_set('expose_php', '0');
ini_set('session.cookie_httponly', '1');
ini_set('session.cookie_secure', '1');
ini_set('session.use_strict_mode', '1');
ini_set('session.cookie_samesite', 'Strict');