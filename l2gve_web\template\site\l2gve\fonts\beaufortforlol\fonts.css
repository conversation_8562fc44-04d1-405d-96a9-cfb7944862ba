@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-light.eot');
	src: local('☺'), url('fonts/beaufortforlol-light.woff') format('woff'), url('fonts/beaufortforlol-light.ttf') format('truetype'), url('fonts/beaufortforlol-light.svg') format('svg');
	font-weight: 300;
	font-style: normal;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-lightitalic.eot');
	src: local('☺'), url('fonts/beaufortforlol-lightitalic.woff') format('woff'), url('fonts/beaufortforlol-lightitalic.ttf') format('truetype'), url('fonts/beaufortforlol-lightitalic.svg') format('svg');
	font-weight: 300;
	font-style: italic;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-regular.eot');
	src: local('☺'), url('fonts/beaufortforlol-regular.woff') format('woff'), url('fonts/beaufortforlol-regular.ttf') format('truetype'), url('fonts/beaufortforlol-regular.svg') format('svg');
	font-weight: normal;
	font-style: normal;
}


@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-italic.eot');
	src: local('☺'), url('fonts/beaufortforlol-italic.woff') format('woff'), url('fonts/beaufortforlol-italic.ttf') format('truetype'), url('fonts/beaufortforlol-italic.svg') format('svg');
	font-weight: normal;
	font-style: italic;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-medium.eot');
	src: local('☺'), url('fonts/beaufortforlol-medium.woff') format('woff'), url('fonts/beaufortforlol-medium.ttf') format('truetype'), url('fonts/beaufortforlol-medium.svg') format('svg');
	font-weight: 500;
	font-style: normal;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-mediumitalic.eot');
	src: local('☺'), url('fonts/beaufortforlol-mediumitalic.woff') format('woff'), url('fonts/beaufortforlol-mediumitalic.ttf') format('truetype'), url('fonts/beaufortforlol-mediumitalic.svg') format('svg');
	font-weight: 500;
	font-style: italic;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-bold.eot');
	src: local('☺'), url('fonts/beaufortforlol-bold.woff') format('woff'), url('fonts/beaufortforlol-bold.ttf') format('truetype'), url('fonts/beaufortforlol-bold.svg') format('svg');
	font-weight: bold;
	font-style: normal;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-bolditalic.eot');
	src: local('☺'), url('fonts/beaufortforlol-bolditalic.woff') format('woff'), url('fonts/beaufortforlol-bolditalic.ttf') format('truetype'), url('fonts/beaufortforlol-bolditalic.svg') format('svg');
	font-weight: bold;
	font-style: italic;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-heavy.eot');
	src: local('☺'), url('fonts/beaufortforlol-heavy.woff') format('woff'), url('fonts/beaufortforlol-heavy.ttf') format('truetype'), url('fonts/beaufortforlol-heavy.svg') format('svg');
	font-weight: 800;
	font-style: normal;
}

@font-face {
	font-family: 'BeaufortforLOL';
	src: url('fonts/beaufortforlol-heavyitalic.eot');
	src: local('☺'), url('fonts/beaufortforlol-heavyitalic.woff') format('woff'), url('fonts/beaufortforlol-heavyitalic.ttf') format('truetype'), url('fonts/beaufortforlol-heavyitalic.svg') format('svg');
	font-weight: 800;
	font-style: italic;
}