<?php
declare(strict_types=1);

readonly class DatabaseConfig {
    public function __construct(
        public string $host = 'localhost',
        public string $dbName = 'l2gve_web_db',
        public string $username = 'root',
        public string $password = '123456',
        public string $charset = 'utf8mb4',
        public int $port = 3306,
        public array $options = []
    ) {}

    public function getDsn(): string {
        return "mysql:host={$this->host};port={$this->port};dbname={$this->dbName};charset={$this->charset}";
    }

    public function getDefaultOptions(): array {
        return array_merge([
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_PERSISTENT => true, // Connection pooling
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE {$this->charset}_unicode_ci",
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            PDO::MYSQL_ATTR_FOUND_ROWS => true,
            // PHP 8.4 optimizations
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::ATTR_ORACLE_NULLS => PDO::NULL_NATURAL,
            PDO::MYSQL_ATTR_MULTI_STATEMENTS => false, // Security
        ], $this->options);
    }
}

class Database {
    private static ?PDO $conn = null;
    private static ?self $instance = null;
    private readonly DatabaseConfig $config;
    private array $queryCache = [];
    private int $maxCacheSize = 100;

    private function __construct(DatabaseConfig $config = null) {
        $this->config = $config ?? new DatabaseConfig();
    }

    public static function getInstance(DatabaseConfig $config = null): self {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }

    public function getConnection(): PDO {
        if (self::$conn !== null) {
            // Check if connection is still alive
            try {
                self::$conn->query('SELECT 1');
                return self::$conn;
            } catch (PDOException $e) {
                // Connection lost, reconnect
                self::$conn = null;
            }
        }

        try {
            self::$conn = new PDO(
                $this->config->getDsn(),
                $this->config->username,
                $this->config->password,
                $this->config->getDefaultOptions()
            );

            // Set additional MySQL optimizations
            $this->optimizeConnection(self::$conn);

        } catch(PDOException $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw new RuntimeException("Connection failed. Please try again later.", previous: $e);
        }

        return self::$conn;
    }

    private function optimizeConnection(PDO $pdo): void {
        // MySQL performance optimizations
        $optimizations = [
            "SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'",
            "SET SESSION innodb_lock_wait_timeout = 5",
            "SET SESSION query_cache_type = ON",
            "SET SESSION query_cache_size = 67108864", // 64MB
            "SET SESSION tmp_table_size = 67108864",
            "SET SESSION max_heap_table_size = 67108864",
        ];

        foreach ($optimizations as $sql) {
            try {
                $pdo->exec($sql);
            } catch (PDOException $e) {
                // Log but don't fail - some settings might not be available
                error_log("Database optimization warning: " . $e->getMessage());
            }
        }
    }

    public function prepare(string $sql): PDOStatement {
        $cacheKey = md5($sql);

        // Check prepared statement cache
        if (isset($this->queryCache[$cacheKey])) {
            return $this->queryCache[$cacheKey];
        }

        $stmt = $this->getConnection()->prepare($sql);

        // Cache prepared statement
        if (count($this->queryCache) >= $this->maxCacheSize) {
            // Remove oldest entry
            array_shift($this->queryCache);
        }

        $this->queryCache[$cacheKey] = $stmt;
        return $stmt;
    }

    public function query(string $sql): PDOStatement {
        return $this->getConnection()->query($sql);
    }

    public function exec(string $sql): int {
        return $this->getConnection()->exec($sql);
    }

    public function lastInsertId(): string {
        return $this->getConnection()->lastInsertId();
    }

    public function beginTransaction(): bool {
        return $this->getConnection()->beginTransaction();
    }

    public function commit(): bool {
        return $this->getConnection()->commit();
    }

    public function rollback(): bool {
        return $this->getConnection()->rollback();
    }

    public function inTransaction(): bool {
        return $this->getConnection()->inTransaction();
    }
}
?>


