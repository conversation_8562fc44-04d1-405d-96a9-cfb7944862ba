<?php
declare(strict_types=1);

readonly class DatabaseConfig {
    public function __construct(
        public string $host = 'localhost',
        public string $dbName = 'l2gve_web_db',
        public string $username = 'root',
        public string $password = '123456',
        public string $charset = 'utf8mb4'
    ) {}
}

class Database {
    private ?PDO $conn = null;
    
    public function __construct(
        private readonly DatabaseConfig $config = new DatabaseConfig()
    ) {}

    public function getConnection(): PDO {
        if ($this->conn !== null) {
            return $this->conn;
        }

        try {
            $dsn = "mysql:host={$this->config->host};dbname={$this->config->dbName};charset={$this->config->charset}";
            $this->conn = new PDO($dsn, $this->config->username, $this->config->password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config->charset}"
            ]);
        } catch(PDOException $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw new RuntimeException("Connection failed. Please try again later.", previous: $e);
        }
        
        return $this->conn;
    }
}
?>


