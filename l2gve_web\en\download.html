<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download - L2GVE Essence GvE x100</title>
    <meta name="description" content="Download L2GVE game client, patches, and get installation instructions for the best Lineage II experience.">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="../template/site/l2gve/images/favicon/favicon.ico" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="stylesheet" href="../template/site/l2gve/fonts/beaufortforlol/fonts.css" />
    <link rel="stylesheet" href="../template/site/l2gve/fonts/intro/stylesheet.css" />
    
    <!-- CSS -->
    <link rel="stylesheet" href="../template/site/l2gve/libs/gwi/css/gwi.css" />
    <link rel="stylesheet" href="../template/site/l2gve/css/style.css" />
    
    <style>
        .download-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #0f1416 0%, #1a2328 100%);
            padding: 2rem 0;
        }
        
        .download-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .download-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .download-title {
            font-size: 3rem;
            color: #ffffff;
            font-family: 'BeaufortforLOL', sans-serif;
            margin-bottom: 1rem;
        }
        
        .download-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }
        
        .download-card {
            background: rgba(15, 20, 22, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .download-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .download-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .download-card-title {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            font-family: 'BeaufortforLOL', sans-serif;
        }
        
        .download-card-desc {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .download-btn {
            display: inline-block;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #000;
            padding: 1rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .download-btn:hover {
            background: linear-gradient(135deg, #ffed4e, #ffd700);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
        }
        
        .download-btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .download-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 215, 0, 0.5);
        }
        
        .system-requirements {
            background: rgba(15, 20, 22, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .requirements-title {
            color: #ffffff;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            font-family: 'BeaufortforLOL', sans-serif;
        }
        
        .requirements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .requirement-label {
            color: rgba(255, 215, 0, 0.8);
            font-weight: 500;
            min-width: 80px;
        }
        
        .requirement-value {
            color: rgba(255, 255, 255, 0.8);
        }
        
        @media (max-width: 768px) {
            .download-title {
                font-size: 2rem;
            }
            
            .download-grid {
                grid-template-columns: 1fr;
            }
            
            .requirements-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="download-page">
        <div class="download-container">
            <!-- Header -->
            <div class="download-header">
                <h1 class="download-title">Download L2GVE</h1>
                <p class="download-subtitle">Get everything you need to start your Lineage II adventure on our Essence GvE x100 server</p>
            </div>
            
            <!-- Download Options -->
            <div class="download-grid">
                <div class="download-card">
                    <div class="download-icon">💾</div>
                    <h3 class="download-card-title">Game Client</h3>
                    <p class="download-card-desc">Full L2GVE game client with all necessary files and latest updates included.</p>
                    <a href="#" class="download-btn">Download Client (2.5 GB)</a>
                </div>
                
                <div class="download-card">
                    <div class="download-icon">🔄</div>
                    <h3 class="download-card-title">Latest Patch</h3>
                    <p class="download-card-desc">Update your existing client with the latest patches and bug fixes.</p>
                    <a href="#" class="download-btn download-btn-secondary">Download Patch (150 MB)</a>
                </div>
                
                <div class="download-card">
                    <div class="download-icon">📋</div>
                    <h3 class="download-card-title">Installation Guide</h3>
                    <p class="download-card-desc">Step-by-step instructions for installing and configuring the game client.</p>
                    <a href="#" class="download-btn download-btn-secondary">View Guide</a>
                </div>
            </div>
            
            <!-- System Requirements -->
            <div class="system-requirements">
                <h3 class="requirements-title">System Requirements</h3>
                <div class="requirements-grid">
                    <div class="requirement-item">
                        <span class="requirement-label">OS:</span>
                        <span class="requirement-value">Windows 7/8/10/11 (64-bit)</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-label">CPU:</span>
                        <span class="requirement-value">Intel Core i3 / AMD FX-6300</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-label">RAM:</span>
                        <span class="requirement-value">4 GB minimum, 8 GB recommended</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-label">GPU:</span>
                        <span class="requirement-value">DirectX 9.0c compatible</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-label">Storage:</span>
                        <span class="requirement-value">15 GB available space</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-label">Network:</span>
                        <span class="requirement-value">Broadband Internet connection</span>
                    </div>
                </div>
            </div>
            
            <!-- Back to Home -->
            <div style="text-align: center;">
                <a href="index.html" class="download-btn download-btn-secondary">← Back to Home</a>
            </div>
        </div>
    </div>
</body>
</html>
