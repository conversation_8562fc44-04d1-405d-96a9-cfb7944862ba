<?php
declare(strict_types=1);

$allowedHosts = ['127.0.0.1', '::1', 'localhost'];
$allowedDomains = ['l2gve.local', 'localhost'];

$isAllowed = in_array($_SERVER['REMOTE_ADDR'], $allowedHosts) || 
             in_array($_SERVER['HTTP_HOST'], $allowedDomains);

if (!$isAllowed) {
    die('Access denied');
}

echo "<h2>Setup Cache Directory</h2>";
echo "<pre>";

$cacheDir = __DIR__ . '/../cache/';

try {
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
        echo "✓ Cache directory created: {$cacheDir}\n";
    } else {
        echo "✓ Cache directory already exists\n";
    }
    
    // Test write permissions
    $testFile = $cacheDir . 'test.txt';
    if (file_put_contents($testFile, 'test') !== false) {
        unlink($testFile);
        echo "✓ Cache directory is writable\n";
    } else {
        echo "✗ Cache directory is not writable\n";
    }
    
    echo "\nCache setup completed!\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "</pre>";
echo "<p><a href='./'>← Back to Admin Panel</a></p>";